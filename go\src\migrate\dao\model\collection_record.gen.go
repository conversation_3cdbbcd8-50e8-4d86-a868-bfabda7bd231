// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCollectionRecord = "collection_record"

// CollectionRecord mapped from table <collection_record>
type CollectionRecord struct {
	ID       int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	AdminID  int32     `gorm:"column:admin_id;not null;comment:管理员id" json:"admin_id"`  // 管理员id
	BillID   int32     `gorm:"column:bill_id;not null;comment:账单id" json:"bill_id"`     // 账单id
	Result   string    `gorm:"column:result;not null;comment:催收结果" json:"result"`       // 催收结果
	Note     string    `gorm:"column:note;not null;comment:小记" json:"note"`             // 小记
	CreateAt time.Time `gorm:"column:create_at;not null;comment:创建时间" json:"create_at"` // 创建时间
}

// TableName CollectionRecord's table name
func (*CollectionRecord) TableName() string {
	return TableNameCollectionRecord
}
