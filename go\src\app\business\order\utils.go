package order

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"time"

	"fincore/model"
	"fincore/route/middleware"
	"fincore/utils/convert"
	"fincore/utils/jsonschema"
	"fincore/utils/log"

	"github.com/gin-gonic/gin"
)

const (
	// 订单放款锁
	OrderDisbursementLockKey = "order_disbursement_lock"
	// 订单检查锁
	OrderCheckLockKey = "order_check_lock"
	// 订单创建锁
	OrderCreationLockKey = "order_creation_lock"
)

// RiskControlClient 风控API客户端
type RiskControlClient struct {
	BaseURL string
	APIKey  string
	Timeout time.Duration
	Enabled bool // 是否启用真实风控服务
}

// RiskScoreRequest 风控评分请求参数
type RiskScoreRequest struct {
	UserID     int     `json:"user_id"`
	ChannelID  int     `json:"channel_id"`
	LoanAmount float64 `json:"loan_amount"`
	Timestamp  int64   `json:"timestamp"`
}

// RiskScoreResponse 风控评分响应
type RiskScoreResponse struct {
	Code      int            `json:"code"`
	Message   string         `json:"message"`
	Data      *RiskScoreData `json:"data"`
	RequestID string         `json:"request_id"`
}

// RiskScoreData 风控评分数据
type RiskScoreData struct {
	UserID    int     `json:"user_id"`
	RiskScore float64 `json:"risk_score"` // 风控评分 0-100
	RiskLevel string  `json:"risk_level"` // 风险等级: LOW, MEDIUM, HIGH
	IsPass    bool    `json:"is_pass"`    // 是否通过风控
}

// LoanRule 放款规则结构
type LoanRule struct {
	RuleID       uint64  `json:"rule_id"`        // 产品规则ID
	MinRiskScore float64 `json:"min_risk_score"` // 风控评分下限
	MaxRiskScore float64 `json:"max_risk_score"` // 风控评分上限
}

// 全局风控客户端实例
var defaultRiskControlClient *RiskControlClient

// GetDefaultRiskControlClient 获取默认风控客户端
func GetDefaultRiskControlClient() *RiskControlClient {
	if defaultRiskControlClient == nil {
		defaultRiskControlClient = &RiskControlClient{
			BaseURL: "", // 从配置文件或环境变量读取
			APIKey:  "", // 从配置文件或环境变量读取
			Timeout: 10 * time.Second,
			Enabled: false, // 默认关闭，使用模拟数据
		}
		// TODO: 从配置文件加载配置
		// defaultRiskControlClient.BaseURL = config.GetString("risk_control.base_url")
		// defaultRiskControlClient.APIKey = config.GetString("risk_control.api_key")
		// defaultRiskControlClient.Enabled = config.GetBool("risk_control.enabled")
	}
	return defaultRiskControlClient
}

// GetUserRiskScore 获取用户风控评分
func (c *RiskControlClient) GetUserRiskScore(userID int, channelID int, loanAmount float64) (float64, error) {
	// 如果未启用真实风控服务，返回模拟数据
	if !c.Enabled || c.BaseURL == "" {
		return c.getMockRiskScore(userID, channelID, loanAmount), nil
	}

	// 调用真实风控API
	return c.callRiskControlAPI(userID, channelID, loanAmount)
}

// getMockRiskScore 获取模拟风控评分
func (c *RiskControlClient) getMockRiskScore(userID int, channelID int, loanAmount float64) float64 {
	log.Order().WithFields(
		log.UserID(userID),
		log.Int("channel_id", channelID),
		log.Amount(loanAmount),
		log.String("operation", "mock_risk_score"),
	).Debug("使用模拟风控评分")

	// 基础评分 70分
	baseScore := 70.0

	// 根据用户ID模拟个人信用（简单哈希）
	userFactor := float64((userID*7)%20) - 10 // -10 到 +10

	// 根据申请金额调整（金额越大风险越高）
	amountFactor := 0.0
	if loanAmount > 15000 {
		amountFactor = -10.0
	} else if loanAmount > 10000 {
		amountFactor = -5.0
	} else if loanAmount < 5000 {
		amountFactor = 5.0
	}

	// 根据渠道调整
	channelFactor := 0.0
	switch channelID {
	case 1: // 宏盛渠道
		channelFactor = 5.0
	case 2: // 公众号渠道
		channelFactor = 0.0
	default:
		channelFactor = -3.0
	}

	// 添加随机因素
	randomFactor := (rand.Float64() - 0.5) * 10 // -5 到 +5

	finalScore := baseScore + userFactor + amountFactor + channelFactor + randomFactor

	// 确保在0-100范围内
	if finalScore < 0 {
		finalScore = 0
	} else if finalScore > 100 {
		finalScore = 100
	}

	log.Order().WithFields(
		log.UserID(userID),
		log.Float64("final_score", finalScore),
		log.String("operation", "mock_risk_score_result"),
	).Debug("模拟风控评分结果")
	return finalScore
}

// callRiskControlAPI 调用真实风控API
func (c *RiskControlClient) callRiskControlAPI(userID int, channelID int, loanAmount float64) (float64, error) {
	log.Order().WithFields(
		log.UserID(userID),
		log.Int("channel_id", channelID),
		log.Amount(loanAmount),
		log.String("operation", "call_risk_api"),
	).Info("调用风控API")

	// 构建请求参数
	request := &RiskScoreRequest{
		UserID:     userID,
		ChannelID:  channelID,
		LoanAmount: loanAmount,
		Timestamp:  time.Now().Unix(),
	}

	// 序列化请求体
	requestBody, err := json.Marshal(request)
	if err != nil {
		return 0, fmt.Errorf("序列化请求参数失败: %v", err)
	}

	// 构建HTTP请求
	url := fmt.Sprintf("%s/api/risk/score", c.BaseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return 0, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	if c.APIKey != "" {
		httpReq.Header.Set("Authorization", "Bearer "+c.APIKey)
	}
	httpReq.Header.Set("User-Agent", "FinCore/1.0")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: c.Timeout,
	}

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		log.Order().WithFields(
			log.UserID(userID),
			log.String("operation", "risk_api_call"),
			log.ErrorField(err),
		).Error("风控API调用失败")
		// 降级到模拟数据
		return c.getMockRiskScore(userID, channelID, loanAmount), nil
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Order().WithFields(
			log.UserID(userID),
			log.String("operation", "read_risk_api_response"),
			log.ErrorField(err),
		).Error("读取风控API响应失败")
		// 降级到模拟数据
		return c.getMockRiskScore(userID, channelID, loanAmount), nil
	}

	// 解析响应
	var response RiskScoreResponse
	err = json.Unmarshal(responseBody, &response)
	if err != nil {
		log.Order().WithFields(
			log.UserID(userID),
			log.String("operation", "parse_risk_api_response"),
			log.ErrorField(err),
		).Error("解析风控API响应失败")
		// 降级到模拟数据
		return c.getMockRiskScore(userID, channelID, loanAmount), nil
	}

	// 检查响应状态
	if response.Code != 200 || response.Data == nil {
		log.Order().WithFields(
			log.UserID(userID),
			log.Int("response_code", response.Code),
			log.String("response_message", response.Message),
			log.String("operation", "risk_api_error"),
		).Error("风控API返回错误")
		// 降级到模拟数据
		return c.getMockRiskScore(userID, channelID, loanAmount), nil
	}

	log.Order().WithFields(
		log.UserID(userID),
		log.Float64("risk_score", response.Data.RiskScore),
		log.String("risk_level", response.Data.RiskLevel),
		log.Bool("is_pass", response.Data.IsPass),
		log.String("operation", "risk_api_success"),
	).Info("风控API调用成功")

	return response.Data.RiskScore, nil
}

// IsRiskScorePass 判断风控评分是否通过
func (c *RiskControlClient) IsRiskScorePass(riskScore float64) bool {
	// 60分以上通过
	return riskScore >= 60.0
}

// GetRiskLevel 根据评分获取风险等级
func (c *RiskControlClient) GetRiskLevel(riskScore float64) string {
	if riskScore >= 80 {
		return "LOW"
	} else if riskScore >= 60 {
		return "MEDIUM"
	} else {
		return "HIGH"
	}
}

// GetRiskScore 获取用户风控评分（工具函数）
func GetRiskScore(userID, channelID int, loanAmount float64) (float64, error) {
	client := GetDefaultRiskControlClient()
	riskScore, err := client.GetUserRiskScore(userID, channelID, loanAmount)
	if err != nil {
		return 0, fmt.Errorf("风控API调用失败: %v", err)
	}

	// 验证风控评分是否通过
	if !client.IsRiskScorePass(riskScore) {
		return 0, fmt.Errorf("风控评分%.1f未通过，不符合放款条件", riskScore)
	}

	return riskScore, nil
}

// ValidateRiskScore 验证风控评分是否通过（工具函数）
func ValidateRiskScore(riskScore float64) bool {
	client := GetDefaultRiskControlClient()
	return client.IsRiskScorePass(riskScore)
}

// GetChannelByID 根据ID获取渠道信息（复用现有逻辑）
func GetChannelByID(channelID int) (*model.Channel, error) {
	channelService := model.NewChannelService()
	return channelService.GetChannelByID(channelID)
}

// GetProductRuleByRiskScore 根据风控评分获取产品规则ID
func GetProductRuleByRiskScore(channelID int, riskScore float64) (int, error) {
	log.Order().WithFields(
		log.Int("channel_id", channelID),
		log.Float64("risk_score", riskScore),
		log.String("operation", "match_product_rule"),
	).Info("匹配产品规则")

	// 1. 复用现有的渠道查询逻辑
	channel, err := GetChannelByID(channelID)
	if err != nil {
		return 0, fmt.Errorf("查询渠道信息失败: %v", err)
	}

	// 检查渠道状态
	if channel.ChannelStatus != 1 {
		return 0, fmt.Errorf("渠道 %d 已停用", channelID)
	}

	// 2. 解析渠道的放款规则
	loanRules, err := parseLoanLimits(channel.LoanRules)
	if err != nil {
		return 0, fmt.Errorf("解析渠道放款规则失败: %v", err)
	}

	if len(loanRules) == 0 {
		return 0, fmt.Errorf("渠道 %d 未配置放款规则", channelID)
	}

	// 3. 根据风控评分匹配规则
	matchedRuleID, err := matchRuleByScore(loanRules, riskScore)
	if err != nil {
		return 0, fmt.Errorf("风控评分匹配失败: %v", err)
	}

	// 4. 验证产品规则是否存在且有效
	productRuleID, err := validateProductRule(matchedRuleID)
	if err != nil {
		return 0, fmt.Errorf("产品规则验证失败: %v", err)
	}

	log.Order().WithFields(
		log.Int("channel_id", channelID),
		log.Float64("risk_score", riskScore),
		log.Int("matched_rule_id", int(matchedRuleID)),
		log.Int("product_rule_id", productRuleID),
		log.String("operation", "product_rule_match_success"),
	).Info("产品规则匹配成功")

	return productRuleID, nil
}

// parseLoanLimits 解析loan_limits字段
func parseLoanLimits(loanLimitsStr string) ([]LoanRule, error) {
	if loanLimitsStr == "" {
		return nil, fmt.Errorf("loan_limits字段为空")
	}

	// 解析JSON
	var loanRules []LoanRule
	err := json.Unmarshal([]byte(loanLimitsStr), &loanRules)
	if err != nil {
		return nil, fmt.Errorf("解析loan_limits JSON失败: %v", err)
	}

	// 验证规则格式
	for i, rule := range loanRules {
		if rule.RuleID == 0 {
			return nil, fmt.Errorf("第%d个规则的rule_id为空", i+1)
		}
		if rule.MinRiskScore < 0 || rule.MinRiskScore > 100 {
			return nil, fmt.Errorf("第%d个规则的min_risk_score超出范围[0,100]", i+1)
		}
		if rule.MaxRiskScore < 0 || rule.MaxRiskScore > 100 {
			return nil, fmt.Errorf("第%d个规则的max_risk_score超出范围[0,100]", i+1)
		}
		if rule.MinRiskScore > rule.MaxRiskScore {
			return nil, fmt.Errorf("第%d个规则的min_risk_score不能大于max_risk_score", i+1)
		}
	}

	return loanRules, nil
}

// matchRuleByScore 根据评分匹配规则
func matchRuleByScore(loanRules []LoanRule, riskScore float64) (uint64, error) {
	for _, rule := range loanRules {
		if riskScore >= rule.MinRiskScore && riskScore <= rule.MaxRiskScore {
			log.Order().WithFields(
				log.Int("rule_id", int(rule.RuleID)),
				log.Float64("min_risk_score", rule.MinRiskScore),
				log.Float64("max_risk_score", rule.MaxRiskScore),
				log.Float64("user_risk_score", riskScore),
				log.String("operation", "rule_matched"),
			).Info("匹配到规则")
			return rule.RuleID, nil
		}
	}

	return 0, fmt.Errorf("风控评分 %.1f 不在任何规则范围内", riskScore)
}

// validateProductRule 验证产品规则
func validateProductRule(ruleID uint64) (int, error) {
	// 解析规则ID到产品规则ID
	productRuleID, err := parseRuleIDToProductRuleID(ruleID)
	if err != nil {
		return 0, fmt.Errorf("解析规则ID失败: %v", err)
	}

	// 复用现有的产品规则查询逻辑
	productRuleService := model.NewProductRulesService()
	productRule, err := productRuleService.GetProductRuleByID(productRuleID)
	if err != nil {
		return 0, fmt.Errorf("查询产品规则失败: %v", err)
	}

	if productRule == nil {
		return 0, fmt.Errorf("产品规则 %d 不存在", productRuleID)
	}

	// 注意：新的 product_rules 表没有 status 字段，假设所有规则都是有效的

	return productRuleID, nil
}

// parseRuleIDToProductRuleID 解析规则ID到产品规则ID
func parseRuleIDToProductRuleID(ruleID uint64) (int, error) {
	// 建立rule_id到product_rule_id的映射关系
	// 这个映射关系可以配置在数据库或配置文件中
	ruleMapping := map[uint64]int{
		1: 1, // 新用户首贷产品
		2: 2, // 复购用户标准产品
		3: 3, // 高额度产品
	}

	productRuleID, exists := ruleMapping[ruleID]
	if !exists {
		return 0, fmt.Errorf("未找到规则ID %d 对应的产品规则", ruleID)
	}

	return productRuleID, nil
}

// GetChannelLoanRules 获取渠道的放款规则（用于管理界面）
func GetChannelLoanRules(channelID int) ([]LoanRule, error) {
	channel, err := GetChannelByID(channelID)
	if err != nil {
		return nil, err
	}

	return parseLoanLimits(channel.LoanRules)
}

// ValidateRiskScoreInChannel 验证风控评分是否在渠道允许范围内
func ValidateRiskScoreInChannel(channelID int, riskScore float64) (bool, error) {
	loanRules, err := GetChannelLoanRules(channelID)
	if err != nil {
		return false, err
	}

	for _, rule := range loanRules {
		if riskScore >= rule.MinRiskScore && riskScore <= rule.MaxRiskScore {
			return true, nil
		}
	}

	return false, nil
}

// FormatOrderStatus 格式化订单状态文本
func FormatOrderStatus(status int) string {
	switch status {
	case 0:
		return "待放款" // 订单创建后等待风控或人工审核
	case 1:
		return "放款中" // 已经成功放款，等待还款
	case 2:
		return "交易关闭" // 审核未通过的订单
	case 3:
		return "交易完成" // 放款成功且所有还款计划已完成
	default:
		return "未知状态"
	}
}

// FormatReasonForClosure 格式化关单原因文本
func FormatReasonForClosure(reason *int) string {
	if reason == nil {
		return ""
	}

	switch *reason {
	case -1:
		return "风控关闭"
	case 0:
		return "终审拒绝"
	case 1:
		return "法院涉案"
	case 2:
		return "纯白户"
	case 3:
		return "客户失联"
	case 4:
		return "不提供资料"
	case 5:
		return "多余订单"
	case 6:
		return "重新下单"
	case 7:
		return "客户不同意方案"
	default:
		return "未知原因"
	}
}

// MaskIDCard 身份证号脱敏处理
func MaskIDCard(idCard string) string {
	if len(idCard) < 7 {
		return idCard
	}
	// 显示前3位和后4位，中间用*号替代
	return idCard[:3] + "***********" + idCard[len(idCard)-4:]
}

// MaskMobile 手机号脱敏处理
func MaskMobile(mobile string) string {
	if len(mobile) != 11 {
		return mobile
	}
	// 显示前3位和后4位，中间用*号替代
	return mobile[:3] + "****" + mobile[7:]
}

// IsAdminUser 判断是否为管理员用户
func IsAdminUser(ctx *gin.Context) bool {
	// 从上下文中获取用户信息
	getuser, exists := ctx.Get("user")
	if !exists {
		return false
	}

	// 转换为正确的用户信息类型
	user, ok := getuser.(*middleware.UserClaims)
	if !ok {
		return false
	}

	// 调用更完善的管理员判断函数
	isAdmin, err := IsUserAdmin(int(user.ID))
	if err != nil {
		// 记录错误日志，但不影响业务流程
		return false
	}

	return isAdmin
}

// FormatRepaymentStatus 格式化还款状态文本
func FormatRepaymentStatus(status int) string {
	switch status {
	case 0:
		return "未还款"
	case 1:
		return "部分还款"
	case 2:
		return "已还清"
	case 3:
		return "逾期"
	default:
		return "未知状态"
	}
}

// CheckChannelAutoDisbursement 检查渠道是否支持自动放款
func CheckChannelAutoDisbursement(channelID int) (bool, string, error) {
	channel, err := GetChannelByID(channelID)
	if err != nil {
		return false, "", fmt.Errorf("查询渠道信息失败: %v", err)
	}

	if channel.ChannelStatus != 1 {
		return false, "", fmt.Errorf("渠道 %d 已停用", channelID)

	}

	return channel.IsAutoDisbursement(), channel.ChannelName, nil
}

// ProcessRiskControlCheck 处理风控检查
func ProcessRiskControlCheck(userID, channelID int, loanAmount float64) (*RiskCheckResult, error) {
	log.Order().WithFields(
		log.UserID(userID),
		log.Int("channel_id", channelID),
		log.Amount(loanAmount),
		log.String("operation", "risk_control_check"),
	).Info("开始风控检查")

	// 1. 获取风控评分
	client := GetDefaultRiskControlClient()
	riskScore, err := client.GetUserRiskScore(userID, channelID, loanAmount)
	if err != nil {
		return nil, fmt.Errorf("获取风控评分失败: %v", err)
	}

	// 2. 验证风控评分是否在渠道允许范围内
	isInRange, err := ValidateRiskScoreInChannel(channelID, riskScore)
	if err != nil {
		return nil, fmt.Errorf("验证风控评分失败: %v", err)
	}

	// 3. 构建风控检查结果
	result := &RiskCheckResult{
		UserID:        userID,
		ChannelID:     channelID,
		LoanAmount:    loanAmount,
		RiskScore:     riskScore,
		RiskLevel:     client.GetRiskLevel(riskScore),
		IsPass:        isInRange && client.IsRiskScorePass(riskScore),
		CheckTime:     time.Now().Unix(),
		FailureReason: "",
	}

	if !result.IsPass {
		if !client.IsRiskScorePass(riskScore) {
			result.FailureReason = fmt.Sprintf("风控评分%.1f低于通过标准", riskScore)
		} else if !isInRange {
			result.FailureReason = fmt.Sprintf("风控评分%.1f不在渠道允许范围内", riskScore)
		}
	}

	log.Order().WithFields(
		log.UserID(userID),
		log.Float64("risk_score", riskScore),
		log.Bool("is_pass", result.IsPass),
		log.String("failure_reason", result.FailureReason),
		log.String("operation", "risk_control_check_complete"),
	).Info("风控检查完成")

	return result, nil
}

// RiskCheckResult 风控检查结果
type RiskCheckResult struct {
	UserID        int     `json:"user_id"`
	ChannelID     int     `json:"channel_id"`
	LoanAmount    float64 `json:"loan_amount"`
	RiskScore     float64 `json:"risk_score"`
	RiskLevel     string  `json:"risk_level"`
	IsPass        bool    `json:"is_pass"`
	CheckTime     int64   `json:"check_time"`
	FailureReason string  `json:"failure_reason"`
}

// IsUserAdmin 判断用户是否为管理员
// 通过查询用户的角色权限，检查是否包含超级权限"*"
func IsUserAdmin(userID int) (bool, error) {
	// 1. 查询用户的角色ID
	roleIDs, err := model.DB().Table("business_auth_role_access").
		Where("uid", userID).
		Pluck("role_id")
	if err != nil {
		return false, fmt.Errorf("查询用户角色失败: %v", err)
	}

	if roleIDs == nil {
		return false, nil // 用户没有分配任何角色
	}

	// 2. 查询角色的权限规则
	rules, err := model.DB().Table("business_auth_role").
		WhereIn("id", roleIDs.([]interface{})).
		Pluck("rules")
	if err != nil {
		return false, fmt.Errorf("查询角色权限失败: %v", err)
	}

	if rules == nil {
		return false, nil // 角色没有权限规则
	}

	// 3. 检查是否包含超级权限"*"
	rulesSlice, ok := rules.([]interface{})
	if !ok {
		return false, fmt.Errorf("权限规则格式错误")
	}

	for _, rule := range rulesSlice {
		if ruleStr, ok := rule.(string); ok && ruleStr == "*" {
			return true, nil // 找到超级权限
		}
	}

	return false, nil // 没有超级权限
}

// CheckOrderOperationPermission 检查订单操作权限
// 返回值：isAdmin bool, operatorName string, error
func CheckOrderOperationPermission(ctx *gin.Context, order *model.BusinessLoanOrders) (bool, string, error) {
	// 1. 获取当前用户信息
	userClaims, exists := ctx.Get("user")
	if !exists {
		return false, "", fmt.Errorf("用户信息获取失败，请重新登录")
	}

	userInfo := userClaims.(*middleware.UserClaims)

	return CheckOrderOperationPermissionByUserInfo(userInfo, order)
}

func CheckOrderOperationPermissionByUserInfo(userInfo *middleware.UserClaims, order *model.BusinessLoanOrders) (bool, string, error) {
	// 2. 检查是否为管理员
	isAdmin, err := IsUserAdmin(int(userInfo.ID))
	if err != nil {
		return false, "", fmt.Errorf("权限检查失败: %v", err)
	}

	// 3. 如果不是管理员，检查是否为订单的跟进业务员
	if !isAdmin {
		// 检查订单是否分配给当前业务员
		if order.SalesAssigneeID == nil || int(*order.SalesAssigneeID) != int(userInfo.ID) {
			return false, "", fmt.Errorf("权限不足，只能操作自己跟进的订单")
		}
	}

	// 4. 获取操作员名称
	operatorName := "管理员"
	if !isAdmin {
		operatorName = "业务员"
	}

	// 直接使用 userInfo.Name，因为它是 string 类型
	if userInfo.Name != "" {
		operatorName = userInfo.Name
	}

	return isAdmin, operatorName, nil
}

// CheckOrderOperationPermissionByOrderNo 根据订单编号检查订单操作权限
// 这是一个便捷方法，内部会查询订单信息然后调用 CheckOrderOperationPermission
func CheckOrderOperationPermissionByOrderNo(ctx *gin.Context, orderNo string) (*model.BusinessLoanOrders, bool, string, error) {
	// 1. 查询订单信息
	orderService := NewOrderService(ctx)
	order, err := orderService.orderModel.GetOrderByOrderNo(orderNo)
	if err != nil {
		return nil, false, "", fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return nil, false, "", fmt.Errorf("订单不存在")
	}

	// 2. 检查权限
	isAdmin, operatorName, err := CheckOrderOperationPermission(ctx, order)
	if err != nil {
		return nil, false, "", err
	}

	return order, isAdmin, operatorName, nil
}

// ValidateUserOrderLimit 验证用户订单限制（检查数量和金额）
func ValidateUserOrderLimit(userID int, loanAmount float64) error {

	// 查询用户所有在途订单（只包括待放款和放款中的订单）
	// 在途订单状态：0-待放款，1-放款中
	inProgressStatuses := []int{model.OrderStatusPendingDisbursement, model.OrderStatusDisbursed}

	query := model.DB().Table("business_loan_orders").
		Where("user_id", userID).
		Where("status", "IN", inProgressStatuses)

	orders, err := query.Get()
	if err != nil {
		return fmt.Errorf("查询用户在途订单失败: %v", err)
	}

	// 包括当前创建订单，在途订单不能超过1个
	if len(orders) > 1 {
		return errors.New("同时借款中的订单不能超过1个")
	}

	// 检查订单金额限制：总借款金额不能超过5000元
	totalAmount := loanAmount
	for _, order := range orders {
		totalAmount += convert.GetFloatFromMap(order, "loan_amount", 0.0)
	}

	if totalAmount >= 8000.0 {
		return fmt.Errorf("在途订单总借款金额%.2f元已超过限制额度", totalAmount)
	}

	return nil
}

// GenerateDisbursementTransactionNo 生成放款流水号
func GenerateDisbursementTransactionNo() string {
	timestamp := time.Now().UnixNano()
	randomNum := rand.Intn(1000000)
	return fmt.Sprintf("D%d%06d", timestamp, randomNum)
}

// CalculateOverdueDays 计算逾期天数（向上取整）
func CalculateOverdueDays(dueDate time.Time) int {
	if dueDate.After(time.Now()) {
		return 0
	}
	hours := time.Since(dueDate).Hours()
	return int(math.Ceil(hours / 24))
}

// ParamsValidate 参数校验并获取
func ParamsValidateAndGet(ctx *gin.Context, schemaGeter func() jsonschema.Schema) jsonschema.ValidationResult {
	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		return jsonschema.ValidationResult{
			Valid: false,
			Errors: []jsonschema.ValidationError{
				jsonschema.ValidationError{
					Message: "请求参数解析失败:" + err.Error(),
				}},
		}
	}
	schema := schemaGeter()
	validator := jsonschema.NewValidator(schema)

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}

	}
	return validator.Validate(cleanData)
}
