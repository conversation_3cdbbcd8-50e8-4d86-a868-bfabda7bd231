package user

import (
	"errors"
	"fincore/app/business/order"
	_ "fincore/app/common"
	captchaApi "fincore/app/uniapp/captcha"
	"fincore/global"
	"fincore/model"
	"fincore/route/middleware"
	"fincore/thirdparty/sms"
	"fincore/utils/gf"
	"fincore/utils/log"
	"fincore/utils/results"
	"fincore/utils/utilstool/goredis"
	"fmt"
	"reflect"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
)

/**
*使用 Index 是省略路径中的index
*本路径为： /admin/user/login -省去了index
 */
type Index struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传[*]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传[*]
}

var conf string // 短信配置 T为启用第三方接口 F为本地调试
func init() {

	fpath := Index{NoNeedLogin: []string{"Logout", "LoginBySms", "PostSms", "PostBySms"}, NoNeedAuths: []string{"*"}}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

/**
*1.《登录》
 */
func (api *Index) Login(c *gin.Context) {
	param, _ := gf.RequestParam(c)

	log.Info("用户登录, 用户名: %s", param["username"])

	if param["username"] == nil || param["password"] == nil {
		results.Failed(c, "请提交用户账号或密码！", nil)
		return
	}
	username := param["username"].(string)
	password := param["password"].(string)
	res, err := model.DB().Table("business_app_account").Fields("id,name").Where("username", username).First()
	if res == nil || err != nil {
		results.Failed(c, "账号不存在！", nil)
		return
	}
	// 由于数据库中已移除password和salt字段，这里暂时跳过密码验证
	// TODO: 实现新的认证机制
	if password == "" {
		results.Failed(c, "密码不能为空！", nil)
		return
	}
	//token
	token := middleware.GenerateToken(&middleware.UserClaims{
		ID:             res["id"].(int64),
		StandardClaims: jwt.StandardClaims{},
	})
	model.DB().Table("business_app_account").Where("id", res["id"]).Data(map[string]interface{}{"loginstatus": 1, "lastLoginTime": time.Now().Unix(), "lastLoginIp": gf.GetIp(c)}).Update()
	//登录日志
	model.DB().Table("app_login_logs").
		Data(map[string]interface{}{"type": 1, "uid": res["id"], "out_in": "in",
			"createtime": time.Now().Unix(), "loginIP": gf.GetIp(c)}).Insert()
	results.Success(c, "登录成功返回token！", token, nil)
}

/**
* 3.《获取用户》
 */
func (api *Index) GetUserinfo(c *gin.Context) {

	token := c.Request.Header.Get("Authorization")
	if token == "" {
		results.Failed(c, "Token不能为空", nil)
		return
	}

	user := middleware.ParseToken(token)

	if user == nil {
		results.Failed(c, "Token解析失败", nil)
		return
	}

	// 检查数据库连接
	db := model.DB()
	if db == nil {
		results.Failed(c, "数据库连接失败", nil)
		return
	}
	data, err := db.Table("business_app_account").Where("id", user.ID).First()
	if err != nil {
		results.Failed(c, "获取用户信息失败", err)
		return
	}

	if data == nil {
		results.Failed(c, "用户不存在", nil)
		return
	}

	// 获取用户的渠道信息
	var channelCode string
	if channelId, exists := data["channelId"]; exists && channelId != nil {
		if channelIdInt, ok := channelId.(int64); ok && channelIdInt > 0 {
			channelService := model.NewChannelService()
			channel, err := channelService.GetChannelByID(int(channelIdInt))
			if err == nil && channel != nil {
				channelCode = channel.ChannelCode
			} else {
				channelCode = ""
			}
		}
	}

	// 将channelCode添加到返回数据中
	data["channelCode"] = channelCode

	results.Success(c, "获取用户信息成功！", data, nil)
}

/**
* 4 刷新token
 */
func (api *Index) Refreshtoken(c *gin.Context) {
	token := c.Request.Header.Get("Authorization")
	if token == "" {
		results.Failed(c, "Token不能为空", nil)
		return
	}

	// 使用defer和recover来捕获Refresh可能的panic
	var newtoken string
	func() {
		defer func() {
			if r := recover(); r != nil {
				results.Failed(c, "Token刷新失败", r)
				return
			}
		}()
		newtoken = middleware.Refresh(token)
	}()

	if newtoken == "" {
		results.Failed(c, "Token刷新失败", nil)
		return
	}

	results.Success(c, "刷新token", newtoken, nil)
}

/**
*  5退出登录
 */
func (api *Index) Logout(c *gin.Context) {
	token := c.Request.Header.Get("Authorization")
	if token != "" {
		middleware.Refresh(token)
		getuser, _ := c.Get("user") //取值 实现了跨中间件取值
		if getuser != nil {
			user := getuser.(*middleware.UserClaims)
			model.DB().Table("business_app_account").Where("id", user.ID).Data(map[string]interface{}{"loginstatus": 0}).Update()
		}
	}
	results.Success(c, "退出登录", true, nil)
}

/**
* 6.通过短信登录
 */
func (api *Index) PostSms(c *gin.Context) {
	// 获取请求参数
	param, _ := gf.RequestParam(c)

	// 获取手机号、图形验证码和图形验证码 ID
	mobile, ok1 := param["mobile"].(string)
	captcha, ok2 := param["captcha"].(string)
	captchaId, ok3 := param["captchaId"].(string)

	// 检查参数是否完整

	if !ok1 || mobile == "" {
		results.Failed(c, "请提交手机号", nil)
		return
	}
	// 验证手机号格式
	mobilePattern := `^1[3-9]\d{9}$`
	if !gf.MatchRegex(mobile, mobilePattern) {
		results.Failed(c, "手机号格式错误", nil)
		return
	}
	if !ok2 || captcha == "" {
		results.Failed(c, "请提交图形验证码", nil)
		return
	}
	if !ok3 || captchaId == "" {
		results.Failed(c, "请提交图形验证码ID", nil)
		return
	}

	captchaIdStr := captchaId
	err := captchaApi.ValidateCaptcha(captchaIdStr, captcha)
	if err != nil {
		results.FailedWithCode(c, "图形验证码验证失败", err, 2)
		return
	}
	// 图形验证码验证成功，发送短信验证码
	smsSendErr := SendSmsCode(mobile)
	if smsSendErr != nil {
		results.Failed(c, "发送短信验证码失败", smsSendErr.Error())
		return
	}

	// 返回成功消息
	results.Success(c, "短信验证码已发送", nil, nil)
}

// 短信验证登录功能
func (api *Index) PostBySms(c *gin.Context) {
	// 获取请求参数
	param, _ := gf.RequestParam(c)

	// 获取手机号、短信验证码和渠道编码
	mobile, ok1 := param["mobile"].(string)
	code, ok2 := param["code"].(string)

	// 获取渠道编码参数
	var channelCode string
	if cc, exists := param["channelCode"].(string); exists && cc != "" {
		// 前端已经在客户端完成了Base64解码，直接使用
		channelCode = cc
		log.Info("接收到前端解码后的渠道编码: %s", channelCode)
	}
	log.Info("开始用户登录/注册: mobile=%s, hasChannelCode=%v, channelCode=%s", mobile, channelCode != "", channelCode)
	// 检查参数是否完整
	if !ok1 || mobile == "" {
		log.Error("用户登录失败: 手机号为空")
		results.Failed(c, "请提交手机号", nil)
		return
	}
	if !ok2 || code == "" {
		log.Error("用户登录失败: 短信验证码为空 mobile=%s", mobile)
		results.Failed(c, "请提交短信验证码", nil)
		return
	}
	// 验证手机号格式
	mobilePattern := `^1[3-9]\d{9}$`
	if !gf.MatchRegex(mobile, mobilePattern) {
		log.Error("用户登录失败: 手机号格式错误 mobile=%s", mobile)
		results.Failed(c, "手机号格式错误", nil)
		return
	}

	// 验证短信验证码
	err := VerifySmsCode(mobile, code)
	if err != nil {
		log.Error("用户登录失败: 短信验证码验证失败 mobile=%s, err=%v", mobile, err)
		results.Failed(c, "短信验证码验证失败", err)
		return
	}

	now := time.Now()

	// 根据手机号查找用户,去除掉软删除的
	user, err := model.DB().Table("business_app_account").Where("mobile", mobile).First()
	if err != nil {
		log.Error("用户登录失败: 查询用户失败 mobile=%s, err=%v", mobile, err)
		results.Failed(c, "登录失败，用户查询异常", err)
		return
	}

	// 如果用户不存在，创建新用户
	if user == nil {
		log.Info("用户不存在，开始创建新用户: mobile=%s", mobile)

		// 处理渠道信息
		var channelId int64 = 0
		if channelCode != "" {
			log.Info("处理渠道绑定: mobile=%s, channelCode=%s", mobile, channelCode)

			// 查询渠道信息
			channel, err := model.DB().Table("channel").Where("channel_code", channelCode).First()
			if err != nil || channel == nil {
				log.Error("用户注册失败: 渠道编码无效 mobile=%s, channelCode=%s, err=%v", mobile, channelCode, err)
				results.Failed(c, "渠道编码无效", nil)
				return
			}

			// 检查渠道状态，如果被禁用则拒绝注册
			if channelStatus, ok := channel["channel_status"].(int64); ok && channelStatus == 0 {
				log.Error("用户注册失败: 渠道已被禁用 mobile=%s, channelCode=%s, status=%d", mobile, channelCode, channelStatus)
				results.Failed(c, "该渠道已被禁用，无法注册", nil)
				return
			}

			channelId = channel["id"].(int64)
			log.Info("渠道绑定成功: mobile=%s, channelId=%d", mobile, channelId)
		}

		// 使用model层的方法创建业务应用账户，自动设置默认渠道和审核员
		businessAppAccountService := model.NewBusinessAppAccountService()
		userData := map[string]interface{}{
			"mobile":     mobile,
			"createtime": now.Unix(),
			"updatetime": now.Unix(),
		}

		if channelId > 0 {
			userData["channelId"] = channelId
			log.Info("添加渠道ID到用户数据: mobile=%s, channelId=%d", mobile, channelId)
		}

		err := businessAppAccountService.CreateBusinessAppAccountWithDefaults(userData)
		if err != nil {
			log.Error("用户注册失败: 创建用户失败 mobile=%s, err=%v", mobile, err)
			results.Failed(c, "登录失败，创建用户失败", err)
			return
		}

		user, err = model.DB().Table("business_app_account").Where("mobile", mobile).First()
		if user == nil || err != nil {
			log.Error("用户注册失败: 查询新用户失败 mobile=%s, err=%v", mobile, err)
			results.Failed(c, "登录失败，创建用户不存在", err)
			return
		}

		log.Info("用户注册成功: mobile=%s, userId=%v, channelId=%d", mobile, user["id"], channelId)
	} else {
		log.Info("用户已存在，执行登录: mobile=%s, userId=%v", mobile, user["id"])

		// 检查账户是否已注销
		if isCancelled, exists := user["isCancelled"]; exists {
			if cancelled, ok := isCancelled.(int64); ok && cancelled == 1 {
				log.Error("用户登录失败: 账户已注销 mobile=%s, userId=%v", mobile, user["id"])
				results.Failed(c, "登录失败，您的账户已注销", nil)
				return
			}
		}
		// 检查用户是否被软删除
		if deletedAt, exists := user["deletedAt"]; exists {
			if deleted, ok := deletedAt.(int64); ok && deleted != 0 {
				log.Error("用户登录失败: 账户已删除 mobile=%s, userId=%v", mobile, user["id"])
				results.Failed(c, "登录失败，您的账户已被删除", nil)
				return
			}
		}

	}

	// 检查用户是否需要绑定渠道
	if channelId, exists := user["channelId"]; !exists || channelId == nil || channelId.(int64) == 0 {
		// 如果用户没有绑定渠道ID，且登录时传入了channelCode，则绑定渠道
		if channelCode != "" {
			log.Info("用户未绑定渠道，开始绑定渠道: mobile=%s, channelCode=%s", mobile, channelCode)

			// 查询渠道信息
			channelService := model.NewChannelService()
			channel, err := channelService.GetChannelByCode(channelCode)
			if err != nil || channel == nil {
				log.Error("用户登录失败: 渠道编码无效 mobile=%s, channelCode=%s, err=%v", mobile, channelCode, err)
				results.Failed(c, "渠道编码无效", nil)
				return
			}

			// 检查渠道状态，如果被禁用则拒绝绑定
			if channel.ChannelStatus == 0 {
				log.Error("用户登录失败: 渠道已被禁用 mobile=%s, channelCode=%s, status=%d", mobile, channelCode, channel.ChannelStatus)
				results.Failed(c, "该渠道已被禁用，无法登录", nil)
				return
			}

			// 更新用户的渠道绑定
			_, err = model.DB().Table("business_app_account").Where("id", user["id"]).Data(map[string]interface{}{
				"channelId": channel.ID,
			}).Update()

			if err != nil {
				log.Error("用户登录失败: 绑定渠道失败 mobile=%s, channelCode=%s, channelId=%d, err=%v", mobile, channelCode, channel.ID, err)
				results.Failed(c, "绑定渠道失败", err)
				return
			}

			log.Info("用户渠道绑定成功: mobile=%s, channelCode=%s, channelId=%d", mobile, channelCode, channel.ID)
		}
	}

	// 更新用户登录状态
	model.DB().Table("business_app_account").Where("id", user["id"]).Data(map[string]interface{}{
		"loginStatus":   1,
		"lastLoginTime": now.Unix(),
		"lastLoginIp":   gf.GetIp(c),
	}).Update()

	// 生成 JWT token
	token := middleware.GenerateToken(&middleware.UserClaims{
		ID: user["id"].(int64),
		// Accountid:      user["accountID"].(int64),
		StandardClaims: jwt.StandardClaims{},
	})

	// 记录登录日志
	var insID int64
	insID, err = model.DB().Table("app_login_logs").Data(map[string]interface{}{
		"uid":        user["id"],
		"out_in":     "in",
		"createtime": now.Unix(),
		"loginIP":    gf.GetIp(c),
	}).Insert()

	if err != nil || insID == 0 {
		log.Error("用户登录失败: 记录登录日志失败 mobile=%s, err=%v", mobile, err)
		results.Failed(c, "登录失败，记录登录日志失败", err)
		return
	}

	log.Info("用户登录成功: mobile=%s, userId=%v", mobile, user["id"])

	// 返回成功消息和 token
	results.Success(c, "登录成功返回token！", token, map[string]interface{}{"uid": user["id"]})
}

// verifySmsCode 验证短信验证码
func VerifySmsCode(mobile, code string) error {
	// 假设有一个缓存存储短信验证码
	// 这里使用一个简单的示例，实际应用中应使用更安全的存储方式
	// cachedCode, found := smsCodeCache[mobile]
	// if !found || cachedCode != code {
	// 	return fmt.Errorf("短信验证码不匹配")
	// }
	//从redis中获取验证码
	CodeKey := fmt.Sprintf("sms_code:%s", mobile)
	// ctx := context.Background()
	err, cachedCode := goredis.Get(CodeKey)
	if !err {
		return errors.New("redis中获取验证码失败")
	}
	if cachedCode != code {
		return errors.New("短信验证码不匹配")
	}
	return nil
}

// SendSmsCode 发送短信验证码
func SendSmsCode(mobile string) error {

	conf = global.App.Config.Sms.Isused
	//防刷机制 检测60s内该手机号是否有发过验证码 如果有则返回错误
	repeatLock := fmt.Sprintf("send_sms: %s", mobile)
	set, err := goredis.SetNX(repeatLock, "locked", 60*time.Second)
	if err != nil {
		return errors.New("redis设置锁失败")
	}
	if !set {
		return errors.New("请60秒后再试")
	}
	//生成6位随机数字验证码

	smsCode := gf.RandIntString(6)

	// 调用公共接口api,传入生成的短信验证码
	if conf == "T" {
		err = sms.SendSmsCode(mobile, smsCode)
		if err != nil {
			return err
		}
	} else {
		fmt.Println("本地调试短信验证码为：", smsCode)
	}
	//确保重复发送的验证码仅最后一次的有效 并设置验证码有效时间为5min
	CodeKey := fmt.Sprintf("sms_code:%s", mobile)
	ok, err := goredis.SetEX(CodeKey, smsCode, 5*time.Minute)
	if !ok {
		return err
	}
	return nil
}

/**
* 7.注销账户
 */
func (api *Index) CancelAccount(c *gin.Context) {
	// 从gin框架中获取用户ID
	getuser, exists := c.Get("user")
	if !exists {
		results.Failed(c, "用户信息获取失败", nil)
		return
	}

	user := getuser.(*middleware.UserClaims)
	if user == nil {
		results.Failed(c, "用户信息解析失败", nil)
		return
	}

	// 检查用户是否有在途订单
	orderService := order.NewOrderService(c)
	orderStats, err := orderService.GetUserOrderStats(user.ID)
	if err != nil {
		log.Error("注销账户失败: 查询用户订单统计失败 userId=%v, err=%v", user.ID, err)
		results.Failed(c, "注销失败，查询订单信息异常", err)
		return
	}

	// 检查是否有在途订单
	inProgressOrders := gconv.Int(orderStats["in_progress_orders"])
	if inProgressOrders > 0 {
		log.Error("注销账户失败: 用户存在未完结订单 userId=%v, inProgressCount=%d", user.ID, inProgressOrders)
		results.Failed(c, "注销失败，您存在未完结的订单", nil)
		return
	}

	// 更新用户注销状态
	_, err = model.DB(model.WithContext(c)).Table("business_app_account").Where("id", user.ID).Data(map[string]interface{}{
		"isCancelled": 1,
	}).Update()

	if err != nil {
		log.Error("注销账户失败: 更新用户状态失败 userId=%v, err=%v", user.ID, err)
		results.Failed(c, "注销失败，更新用户状态异常", err)
		return
	}

	log.Info("用户注销账户成功: userId=%v", user.ID)
	results.Success(c, "账户注销成功", true, nil)
}
