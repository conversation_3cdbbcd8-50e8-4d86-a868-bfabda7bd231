package order

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/decimal"
	"fincore/utils/gform"
	"fincore/utils/pagination"
	"fincore/utils/shopspringutils"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-module/carbon/v2"
	"golang.org/x/sync/errgroup"
)

// Repository 订单数据仓库层
type Repository struct {
	ctx context.Context
}

// NewRepository 创建订单数据仓库实例
func NewRepository(ctx context.Context) *Repository {
	return &Repository{
		ctx: ctx,
	}
}

// GetOrderByOrderNo 根据订单编号获取订单信息
func (r *Repository) GetOrderByOrderNo(orderNo string) (*model.BusinessLoanOrders, error) {
	orderService := model.NewBusinessLoanOrdersService(r.ctx)
	return orderService.GetOrderByOrderNo(orderNo)
}

// GetBillListByOrderID 根据订单ID获取账单列表
func (r *Repository) GetBillListByOrderID(orderID int, userID int) ([]BillInfo, error) {
	// 查询账单列表，包含支付时间和累计退款金额
	query := model.DB(model.WithContext(r.ctx)).Table("business_repayment_bills brb").
		Fields(`brb.id,
			brb.period_number,
			brb.total_due_amount,
			brb.paid_amount,
			brb.total_refund_amount,
			brb.status,
			brb.due_date,
			brb.total_waive_amount,
			brb.paid_at as payment_time`).
		Where("brb.order_id", orderID).
		Where("brb.user_id", userID).
		OrderBy("brb.period_number ASC")

	data, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询账单列表失败: %v", err)
	}

	var bills []BillInfo
	for _, item := range data {
		bill := BillInfo{
			ID:                convert.GetIntFromMap(item, "id", 0),
			PeriodNumber:      convert.GetIntFromMap(item, "period_number", 0),
			TotalDueAmount:    convert.GetStringFromMap(item, "total_due_amount"),
			PaidAmount:        convert.GetStringFromMap(item, "paid_amount"),
			TotalRefundAmount: convert.GetStringFromMap(item, "total_refund_amount"),
			TotalWaiveAmount:  convert.GetStringFromMap(item, "total_waive_amount"),
			Status:            convert.GetIntFromMap(item, "status", 0),
			DueDate:           convert.GetTimeFromMap(item, "due_date").Format("2006-01-02"),
		}

		// 状态文案
		bill.StatusText = model.GetBillStatusText(bill.Status)

		// 处理支付时间
		if paymentTimeVal := convert.GetTimeFromMap(item, "payment_time"); !paymentTimeVal.IsZero() {
			paymentTime := paymentTimeVal.Format("2006-01-02 15:04:05")
			bill.PaymentTime = &paymentTime
		}

		bills = append(bills, bill)
	}

	return bills, nil
}

// GetDisbursementRecordsByOrderID 根据订单ID获取放款记录
func (r *Repository) GetDisbursementRecordsByOrderID(orderID int, userID int) ([]DisbursementRecord, error) {
	query := model.DB().Table("business_payment_transactions").
		Fields(`id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no`).
		Where("order_id", orderID).
		Where("user_id", userID).
		WhereNull("deleted_at").
		Where("type", "DISBURSEMENT").
		OrderBy("created_at ASC")

	data, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询放款记录失败: %v", err)
	}

	var records []DisbursementRecord
	for _, item := range data {
		record := DisbursementRecord{
			ID:            convert.GetIntFromMap(item, "id", 0),
			Status:        convert.GetIntFromMap(item, "status", 0),
			PayoutAccount: "统统付小贷", // 占位预留
			PaymentType:   "统统付小贷打款",
			Amount:        convert.GetStringFromMap(item, "amount"),
		}

		// 状态文案
		record.StatusText = model.GetTransactionStatusText(record.Status)

		// 处理发起时间
		if initiatedAtVal := convert.GetTimeFromMap(item, "initiated_at"); !initiatedAtVal.IsZero() {
			record.InitiatedAt = initiatedAtVal.Format("2006-01-02 15:04:05")
		}

		// 处理完成时间
		if completedAtVal := convert.GetTimeFromMap(item, "completed_at"); !completedAtVal.IsZero() {
			completedAt := completedAtVal.Format("2006-01-02 15:04:05")
			record.CompletedAt = &completedAt
		}

		// 处理渠道流水号
		if channelTransactionNo := convert.GetStringFromMap(item, "channel_transaction_no"); channelTransactionNo != "" {
			record.ChannelTransactionNo = &channelTransactionNo
		}

		records = append(records, record)
	}

	return records, nil
}

// GetPaymentRecordsByOrderID 根据订单ID获取支付记录（分页）
func (r *Repository) GetPaymentRecordsByOrderID(orderID, page, pageSize int) (*pagination.PaginationResponse, error) {
	// 构建分页请求
	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 创建查询对象
	baseQuery := func() gform.IOrm {
		return model.DB(model.WithContext(r.ctx)).Table("business_payment_transactions bpt").
			Where("bpt.order_id", orderID).
			WhereNull("bpt.deleted_at").
			Where("bpt.type", "IN", []string{"REPAYMENT", "REFUND", "WITHHOLD", "PARTIAL_OFFLINE_REPAYMENT", "MANUAL_WITHHOLD"})
	}

	countQuery := baseQuery()
	dataQuery := baseQuery().
		Fields(`bpt.id,
			bpt.transaction_no,
			bpt.amount,
			bpt.status,
			bpt.type,
			bpt.withhold_type,
			bpt.created_at as initiated_at,
			bpt.completed_at,
			bpt.channel_transaction_no,
			bpt.error_message,
			bpt.offline_payment_voucher,
			brb.period_number,
			brb.id as bill_id,
			bpc.channel_name as payment_type,
			(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount`).
		LeftJoin("business_repayment_bills brb", "bpt.bill_id = brb.id").
		LeftJoin("business_payment_channels bpc", "bpt.payment_channel_id = bpc.id").
		OrderBy("bpt.created_at DESC")

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("查询支付记录失败: %v", err)
	}

	// 处理查询结果数据
	processedList := r.processPaymentRecords(result.Data.([]gform.Data))
	result.Data = processedList

	return result, nil
}

// processPaymentRecords 处理支付记录数据
func (r *Repository) processPaymentRecords(data []gform.Data) []PaymentRecord {
	var records []PaymentRecord
	for _, item := range data {
		record := PaymentRecord{
			ID:            convert.GetIntFromMap(item, "id", 0),
			TransactionNo: convert.GetStringFromMap(item, "transaction_no"),
			BillID:        convert.GetIntFromMap(item, "bill_id", 0),
			Amount:        convert.GetStringFromMap(item, "amount"),
			Status:        convert.GetIntFromMap(item, "status", 0),
			RefundAmount:  convert.GetStringFromMap(item, "refund_amount"),
			Image:         "", // 预留字段
		}

		record.StatusText = model.GetTransactionStatusText(record.Status)

		// 处理代扣类型
		record.WithholdType = convert.GetStringFromMap(item, "withhold_type")
		record.WithholdTypeText = model.GetWithholdTypeText(record.WithholdType)

		// 处理交易类型
		record.Type = convert.GetStringFromMap(item, "type")

		// 处理支付类型
		record.PaymentType = convert.GetStringFromMap(item, "payment_type")

		// 处理发起时间
		if item["initiated_at"] != nil {
			record.InitiatedAt = item["initiated_at"].(time.Time).Format("2006-01-02 15:04:05")
		}

		// 处理完成时间
		if item["completed_at"] != nil {
			completedAt := item["completed_at"].(time.Time).Format("2006-01-02 15:04:05")
			record.CompletedAt = &completedAt
		}

		// 处理期数
		if periodNumber := convert.GetIntFromMap(item, "period_number", 0); periodNumber > 0 {
			record.PeriodNumber = &periodNumber
		}

		// 处理渠道流水号
		if channelTransactionNo := convert.GetStringFromMap(item, "channel_transaction_no"); channelTransactionNo != "" {
			record.ChannelTransactionNo = channelTransactionNo
		}

		// 处理错误信息
		if errorMessage := convert.GetStringFromMap(item, "error_message"); errorMessage != "" {
			record.ErrorMessage = errorMessage
		}

		// 图片
		if offlinePaymentVoucher := convert.GetStringFromMap(item, "offline_payment_voucher"); offlinePaymentVoucher != "" {
			record.Image = offlinePaymentVoucher
		}

		records = append(records, record)
	}

	return records
}

// GetBillByIDInTransaction 根据账单ID获取账单信息（在事务中查询）
func (r *Repository) GetBillByIDInTransaction(tx gform.IOrm, billID int) (*model.BusinessRepaymentBills, error) {
	var bill model.BusinessRepaymentBills

	data, err := tx.Table("business_repayment_bills").
		Where("id", billID).
		First()

	if err != nil {
		return nil, fmt.Errorf("查询账单失败: %v", err)
	}

	if data == nil {
		return nil, nil
	}

	// 转换数据
	bill.ID = convert.MustConvertToInt(data["id"], 0)
	bill.OrderID = convert.MustConvertToInt(data["order_id"], 0)
	bill.UserID = convert.MustConvertToInt(data["user_id"], 0)
	bill.PeriodNumber = convert.MustConvertToInt(data["period_number"], 0)
	bill.DuePrincipal = model.Decimal(convert.MustConvertToFloat(data["due_principal"], 0))
	bill.DueInterest = model.Decimal(convert.MustConvertToFloat(data["due_interest"], 0))
	bill.DueGuaranteeFee = model.Decimal(convert.MustConvertToFloat(data["due_guarantee_fee"], 0))
	bill.DueOtherFees = model.Decimal(convert.MustConvertToFloat(data["due_other_fees"], 0))
	bill.AssetManagementEntry = model.Decimal(convert.MustConvertToFloat(data["asset_management_entry"], 0))
	bill.LateFee = model.Decimal(convert.MustConvertToFloat(data["late_fee"], 0))
	bill.TotalDueAmount = model.Decimal(convert.MustConvertToFloat(data["total_due_amount"], 0))
	bill.PaidAmount = model.Decimal(convert.MustConvertToFloat(data["paid_amount"], 0))
	bill.Status = convert.MustConvertToInt(data["status"], 0)
	bill.TotalWaiveAmount = model.Decimal(convert.MustConvertToFloat(data["total_waive_amount"], 0))
	bill.DueDate = convert.GetTimeFromMap(data, "due_date")

	return &bill, nil
}

// GetOrderByIDInTransaction 根据订单ID获取订单信息（在事务中查询）
func (r *Repository) GetOrderByIDInTransaction(tx gform.IOrm, orderID int) (*model.BusinessLoanOrders, error) {
	var order model.BusinessLoanOrders

	data, err := tx.Table("business_loan_orders").
		Where("id", orderID).
		First()

	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}

	if data == nil {
		return nil, nil
	}

	// 转换数据
	order.ID = uint(convert.MustConvertToInt(data["id"], 0))
	order.OrderNo = convert.ConvertToString(data["order_no"])
	order.UserID = uint(convert.MustConvertToInt(data["user_id"], 0))
	order.ProductRuleID = uint(convert.MustConvertToInt(data["product_rule_id"], 0))
	order.LoanAmount = decimal.Decimal(convert.MustConvertToFloat(data["loan_amount"], 0))
	order.Principal = decimal.Decimal(convert.MustConvertToFloat(data["principal"], 0))
	order.TotalInterest = decimal.Decimal(convert.MustConvertToFloat(data["total_interest"], 0))
	order.TotalGuaranteeFee = decimal.Decimal(convert.MustConvertToFloat(data["total_guarantee_fee"], 0))
	order.TotalOtherFees = decimal.Decimal(convert.MustConvertToFloat(data["total_other_fees"], 0))
	order.TotalRepayableAmount = decimal.Decimal(convert.MustConvertToFloat(data["total_repayable_amount"], 0))
	order.AmountPaid = decimal.Decimal(convert.MustConvertToFloat(data["amount_paid"], 0))
	order.Status = int8(convert.MustConvertToInt(data["status"], 0))

	return &order, nil
}

// UpdateBillAmounts 更新账单金额信息
func (r *Repository) UpdateBillAmounts(tx gform.IOrm, billID int, updateData map[string]interface{}) error {
	_, err := tx.Table("business_repayment_bills").
		Where("id", billID).
		Data(updateData).
		Update()

	if err != nil {
		return fmt.Errorf("更新账单金额失败: %v", err)
	}

	return nil
}

// UpdateOrderAmounts 更新订单金额信息
func (r *Repository) UpdateOrderAmounts(tx gform.IOrm, orderID int, updateData map[string]interface{}) error {
	_, err := tx.Table("business_loan_orders").
		Where("id", orderID).
		Data(updateData).
		Update()

	if err != nil {
		return fmt.Errorf("更新订单金额失败: %v", err)
	}

	return nil
}

// CreateOperationLogInTransaction 在事务中创建操作日志
func (r *Repository) CreateOperationLogInTransaction(tx gform.IOrm, orderID int, operatorID int, operatorName, action, details string) error {
	logData := map[string]interface{}{
		"order_id":      orderID,
		"operator_id":   operatorID,
		"operator_name": operatorName,
		"action":        action,
		"details":       details,
		"created_at":    time.Now().Unix(),
	}

	_, err := tx.Table("business_order_operation_logs").
		Data(logData).
		Insert()

	if err != nil {
		return fmt.Errorf("创建操作日志失败: %v", err)
	}

	return nil
}

// UpdateBillDueDate 更新账单到期时间
func (r *Repository) UpdateBillDueDate(billID int, dueDate time.Time) error {
	// 使用model层更新账单到期时间
	billService := model.NewBusinessRepaymentBillsService(r.ctx)

	// 先检查账单是否存在
	bill, err := billService.GetBillByID(billID)
	if err != nil {
		return fmt.Errorf("查询账单失败: %v", err)
	}
	if bill == nil {
		return fmt.Errorf("账单不存在")
	}

	// 判断账单是否可更新到期时间，满足条件 账单状态: 0-待支付；3-逾期待支付；7-部分还款；9-逾期部分支付
	if bill.Status != model.RepaymentBillStatusUnpaid &&
		bill.Status != model.RepaymentBillStatusOverdueUnpaid &&
		bill.Status != model.RepaymentBillStatusPartialPaid &&
		bill.Status != model.RepaymentBillStatusOverduePartialPaid {
		return fmt.Errorf("账单已完结，不可更新到期时间")
	}

	// 更新账单到期时间
	newBillStatus := bill.Status

	carbonDueDate := carbon.CreateFromStdTime(dueDate)
	carbonNow := carbon.Now()

	//  如果账单未支付，修改时间为今天之前，则将账单状态改为逾期未支付
	if carbonDueDate.DiffInDays(carbonNow) > 0 && bill.Status == model.RepaymentBillStatusUnpaid {
		newBillStatus = model.RepaymentBillStatusOverdueUnpaid
	}

	// 如果账单逾期待支付，修改时间为今天或今天以后，则将账单状态改为待支付
	if carbonDueDate.DiffInDays(carbonNow) <= 0 && bill.Status == model.RepaymentBillStatusOverdueUnpaid {
		newBillStatus = model.RepaymentBillStatusUnpaid

	}

	// 如果账单为部分支付，修改时间为今天以前，则将账单状态改为逾期部分支付
	if carbonDueDate.DiffInDays(carbonNow) > 0 && bill.Status == model.RepaymentBillStatusPartialPaid {
		newBillStatus = model.RepaymentBillStatusOverduePartialPaid
	}

	// 如果账单为逾期部分支付，修改时间为今天或今天以后，则将账单状态改为部分支付
	if carbonDueDate.DiffInDays(carbonNow) <= 0 && bill.Status == model.RepaymentBillStatusOverduePartialPaid {
		newBillStatus = model.RepaymentBillStatusPartialPaid
	}

	// 使用原生SQL更新到期时间
	_, err = model.DB().Table("business_repayment_bills").
		Where("id", billID).
		Update(map[string]interface{}{
			"due_date": dueDate,
			"status":   newBillStatus,
		})
	if err != nil {
		return fmt.Errorf("更新账单到期时间失败: %v", err)
	}

	return nil
}

// GetRiskPassedTimeByUserID 根据用户ID获取风控通过时间
func (r *Repository) GetRiskPassedTimeByUserID(userID int) (*string, error) {
	query := `
		SELECT updated_at
		FROM risk_evaluations
		WHERE customer_id = ? AND risk_result = 0
		ORDER BY updated_at DESC
		LIMIT 1
	`

	result, err := model.DB().Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("查询风控评估失败: %v", err)
	}

	if len(result) == 0 {
		// 没有风控通过记录，返回nil
		return nil, nil
	}

	updatedAt, ok := result[0]["updated_at"]
	if !ok || updatedAt == nil {
		return nil, nil
	}

	// 处理时间格式转换
	var timeStr string
	switch v := updatedAt.(type) {
	case string:
		timeStr = v
	case []byte:
		timeStr = string(v)
	default:
		return nil, fmt.Errorf("时间格式错误")
	}

	// 尝试解析时间并格式化
	if parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
		formatted := parsedTime.Format("2006-01-02 15:04:05")
		return &formatted, nil
	}

	// 如果解析失败，直接返回原始字符串
	return &timeStr, nil
}

// GetCustomerNameByUserID 根据用户ID获取客户姓名
func (r *Repository) GetCustomerNameByUserID(userID int) (string, error) {
	query := `
		SELECT name
		FROM business_app_account
		WHERE id = ?
	`

	result, err := model.DB().Query(query, userID)
	if err != nil {
		return "", fmt.Errorf("查询客户信息失败: %v", err)
	}

	if len(result) == 0 {
		return "", fmt.Errorf("客户不存在")
	}

	name, ok := result[0]["name"].(string)
	if !ok {
		return "", fmt.Errorf("客户姓名格式错误")
	}

	return name, nil
}

// GetSalesAssigneeNameByID 根据业务员ID获取业务员名称
func (r *Repository) GetSalesAssigneeNameByID(salesAssigneeID int) (string, error) {
	query := `
		SELECT name
		FROM business_account
		WHERE id = ?
	`

	result, err := model.DB().Query(query, salesAssigneeID)
	if err != nil {
		return "", fmt.Errorf("查询业务员信息失败: %v", err)
	}

	if len(result) == 0 {
		return "", fmt.Errorf("业务员不存在")
	}

	name, ok := result[0]["name"].(string)
	if !ok {
		return "", fmt.Errorf("业务员姓名格式错误")
	}

	return name, nil
}

// GetOrderListWithFilters 获取订单列表（包含账单到期时间筛选）
func (r *Repository) GetOrderListWithFilters(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 创建两个独立的查询对象，避免Count()污染数据查询
	countQuery := r.buildOrderListQuery(params)
	dataQuery := r.buildOrderListQuery(params).
		Fields(`blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,
		blo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,
		blo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,
		blo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,
	    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,
		blo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,
		baa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,
		c1.channel_name as channel_name, bpc.channel_name as payment_channel_name,
		c2.channel_name as initial_order_channel_name,
		pr.loan_period as loan_period, pr.total_periods as total_periods,
		 sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,
		(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods`).
		OrderBy("blo.id DESC")

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("获取订单列表失败: %v", err)
	}

	return result, nil
}

// GetDueBillListWithFilters 获取到期账单列表
func (r *Repository) GetDueBillListWithFilters(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 创建两个独立的查询对象，避免Count()污染数据查询
	countQuery := r.buildDueBillListQuery(params)
	//订单编号、渠道来源、审核人员、催收人员、是否复购、复购轮次、催收标签（展示催收记录）、姓名/手机号、到期账单（1/2）、账单总金额、已付金额、到期账单状态、支付时间、到期时间
	dataQuery := r.buildDueBillListQuery(params).
		Fields(`brb.id as id, 
		brb.user_id as user_id, 
		brb.order_id as order_id,
		blo.order_no as order_no,
		c.channel_name as channel_name, 
		sales_user.username as sales_assignee_name,
		collection_user.username as collection_assignee_name,
		baa.name as user_name, 
		baa.mobile as user_mobile,
		brb.total_due_amount as total_due_amount,
		brb.paid_amount as paid_amount,
		DATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,
		DATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,
		brb.status as status,
		brb.period_number as period_number,
		pr.total_periods as total_periods,
		(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num`).
		OrderBy("brb.id DESC")

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("获取到期账单列表失败: %v", err)
	}
	for _, item := range result.Data.([]gform.Data) {
		item["periods"] = fmt.Sprintf("%d/%d", item["period_number"], item["total_periods"])
		if item["repeat_buy_num"].(int64) > 0 {
			item["is_repeat_buy"] = 1
		} else {
			item["is_repeat_buy"] = 0
		}
	}

	return result, nil
}

// GetOverdueBillListWithFilters 获取逾期账单列表
func (r *Repository) GetOverdueBillListWithFilters(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 创建两个独立的查询对象，避免Count()污染数据查询
	countQuery := r.buildOverdueBillListQuery(params)
	//展示字段。订单编号、渠道来源、催收、姓名/手机号、身份证号、已付期数（0/2）、已付金额、合计金额、逾期日期/天数（2025年7月8日，逾期1天）、成本、差值（合计金额-已付金额）、审核、下单时间、最近登录时间（日期、时间、IP归属地）
	dataQuery := r.buildOverdueBillListQuery(params).
		Fields(`brb.id as id, 
		brb.user_id as user_id, 
		blo.order_no as order_no,
		brb.order_id as order_id,
		c.channel_name as channel_name, 
		sales_user.username as sales_assignee_name,
		collection_user.username as collection_assignee_name,
		baa.name as user_name, 
		baa.mobile as user_mobile,
		baa.idCard as id_card,
		FROM_UNIXTIME(baa.lastLoginTime, '%Y-%m-%d %H:%i:%s') as last_login_time,
		brb.total_due_amount as total_due_amount,
		brb.paid_amount as paid_amount,
		brb.due_principal as due_principal,
		brb.total_due_amount - brb.paid_amount as diff_amount,
		DATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,
		DATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,
		brb.status as status,
		brb.period_number as period_number,
		DATE_FORMAT(brb.created_at,  '%Y-%m-%d %H:%i:%s') as created_at,
		pr.total_periods as total_periods,
		IFNULL((SELECT region FROM user_operate_log WHERE uid = brb.user_id ORDER BY created_at DESC LIMIT 1 ), '未知' ) as region,
		(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id AND business_repayment_bills.status in (1,2)) as paid_periods`).
		OrderBy("brb.id DESC")

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("获取逾期账单列表失败: %v", err)
	}
	for _, item := range result.Data.([]gform.Data) {
		item["periods"] = fmt.Sprintf("%d/%d", item["period_number"], item["total_periods"])
		due, _ := time.Parse("2006-01-02", item["due_date"].(string))
		item["overdue_date"] = fmt.Sprintf("%s，逾期%d天",
			item["due_date"],
			CalculateOverdueDays(due))
		item["paid_periods"] = fmt.Sprintf("%d/%d", item["paid_periods"], item["total_periods"])
		item["last_login_info"] = fmt.Sprintf("%s(%s)", item["last_login_time"], item["region"])
	}

	return result, nil
}

// 到期账单统计数据   应收人数，应收金额，实收人数，实收金额，当日实收金额，当日逾期金额，回款率，逾期率
type DueBillStatistics struct {
	ReceivablePeople   int    `json:"receivable_people"`
	ReceivableAmount   string `json:"receivable_amount"`
	PaidPeople         int    `json:"paid_people"`
	PaidAmount         string `json:"paid_amount"`
	TodayPaidAmount    string `json:"today_paid_amount"`
	TodayOverdueAmount string `json:"today_overdue_amount"`
	PaymentRate        string `json:"payment_rate"`
	OverdueRate        string `json:"overdue_rate"`
}

func (r *Repository) GetDueBillStatisticsWithFilters(params map[string]interface{}) (*DueBillStatistics, error) {
	// 构建查询
	query := r.buildDueBillListQuery(params)

	result, err := query.Fields(`
		COUNT(DISTINCT brb.user_id) AS receivable_people,
		SUM(brb.total_due_amount) AS receivable_amount,
		COUNT(DISTINCT CASE WHEN brb.paid_amount > 0 THEN brb.user_id END) AS paid_people,
		SUM(brb.paid_amount) AS paid_amount,
		SUM(CASE WHEN DATE(brb.paid_at) = brb.due_date THEN brb.paid_amount ELSE 0 END) AS today_paid_amount
	`).Get()

	if err != nil {
		return nil, fmt.Errorf("获取到期账单统计失败: %v", err)
	}

	if len(result) > 0 {
		statistics := &DueBillStatistics{}
		convert.MapToStruct(result[0], statistics)
		receivableAmount := decimal.MustNewDecimalFromString(statistics.ReceivableAmount)
		paidAmount := decimal.MustNewDecimalFromString(statistics.PaidAmount)
		todayOverdueAmout := receivableAmount.Sub(paidAmount)
		statistics.TodayOverdueAmount = todayOverdueAmout.String()
		paymentRate := paidAmount.Div(receivableAmount).Mul(100).Round(2)
		statistics.PaymentRate = paymentRate.String()
		statistics.OverdueRate = paymentRate.Sub(100).Abs().String()
		return statistics, nil
	}

	return nil, nil
}

// buildOrderListQuery 构建订单列表查询对象
func (r *Repository) buildOrderListQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("business_loan_orders blo")

	// 添加关联查询
	query = query.
		LeftJoin("business_app_account baa", "blo.user_id = baa.id").
		Where("baa.deletedAt = 0").
		LeftJoin("channel c1", "blo.channel_id = c1.id").
		LeftJoin("channel c2", "baa.channelId = c2.id").
		LeftJoin("product_rules pr", "blo.product_rule_id = pr.id").
		LeftJoin("business_payment_channels bpc", "blo.payment_channel_id = bpc.id").
		LeftJoin("business_account sales_user", "blo.sales_assignee_id = sales_user.id").
		LeftJoin("business_account collection_user", "blo.collection_assignee_id = collection_user.id")

	// 应用筛选条件
	query = r.applyOrderListFilters(query, params)

	return query
}

// buildDueBillListQuery 构建到期账单列表查询对象
func (r *Repository) buildDueBillListQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("business_repayment_bills brb")

	// 添加关联查询
	query = query.
		LeftJoin("business_app_account baa", "brb.user_id = baa.id").
		LeftJoin("business_loan_orders blo", "brb.order_id = blo.id").
		LeftJoin("channel c", "blo.channel_id = c.id").
		LeftJoin("product_rules pr", "blo.product_rule_id = pr.id").
		LeftJoin("business_account sales_user", "blo.sales_assignee_id = sales_user.id").
		LeftJoin("business_account collection_user", "brb.collection_assignee_id = collection_user.id")

	// 应用筛选条件
	query = r.applyDueBillListFilters(query, params)

	return query
}

// applyDueBillListFilters 应用到期账单列表筛选条件
func (r *Repository) applyDueBillListFilters(query gform.IOrm, params map[string]interface{}) gform.IOrm {
	//筛选信息。a. 审核姓名。审核人员姓名；b. 催收姓名。催收人员姓名。c. 客户姓名。d. 客户手机号。c。 渠道来源。e. 订单状态。待支付、已支付、逾期已支付、逾期待支付。f. 是否复购。g. 到期时间
	// 订单编号查询
	if orderNo, ok := params["order_no"]; ok && orderNo != "" {
		query = query.Where("blo.order_no", "like", fmt.Sprintf("%%%v%%", orderNo))
	}

	// 用户ID查询
	if userID, ok := params["user_id"]; ok && userID != "" {
		query = query.Where("brb.user_id", userID)
	}

	// 用户姓名查询
	if userName, ok := params["user_name"]; ok && userName != "" {
		query = query.Where("baa.name", "like", fmt.Sprintf("%%%v%%", userName))
	}

	// 用户手机号查询
	if userMobile, ok := params["user_mobile"]; ok && userMobile != "" {
		query = query.Where("baa.mobile", "like", fmt.Sprintf("%%%v%%", userMobile))
	}

	// 业务员姓名查询
	if salesAssigneeName, ok := params["sales_assignee_name"]; ok && salesAssigneeName != "" {
		query = query.Where("sales_user.username", "like", fmt.Sprintf("%%%v%%", salesAssigneeName))
	}

	// 业务员姓名查询
	if salesAssigneeID, ok := params["sales_assignee_id"]; ok {
		query = query.Where("sales_user.id", salesAssigneeID)
	}

	// 催收员姓名查询
	if collectionAssigneeName, ok := params["collection_assignee_name"]; ok && collectionAssigneeName != "" {
		query = query.Where("collection_user.username", "like", fmt.Sprintf("%%%v%%", collectionAssigneeName))
	}
	// 催收员id查询
	if collectionAssigneeID, ok := params["collection_assignee_id"]; ok {
		query = query.Where("collection_user.id", collectionAssigneeID)
	}

	// 订单状态查询
	if status, ok := params["status"]; ok {
		query = query.Where("brb.status", status)
	}

	// 渠道名称查询
	if channelName, ok := params["channel_name"]; ok && channelName != "" {
		query = query.Where("c.channel_name", "like", fmt.Sprintf("%%%v%%", channelName))
	}

	// 渠道id查询
	if channelID, ok := params["channel_id"]; ok {
		query = query.Where("c.id", channelID)
	}

	// //到期时间查询
	// if billDueDate, ok := params["due_date"]; ok && billDueDate != "" {
	// 	query = query.Where("brb.due_date", "<=", billDueDate)
	// }

	// 到期时间范围查询
	if billDueDateStart, ok := params["due_date_start"]; ok && billDueDateStart != "" {
		query = query.Where("brb.due_date", ">=", billDueDateStart)
	}

	if billDueDateEnd, ok := params["due_date_end"]; ok && billDueDateEnd != "" {
		query = query.Where("brb.due_date", "<=", billDueDateEnd)
	}

	// 是否复购
	if isRepeatBuy, ok := params["is_repeat_buy"]; ok && isRepeatBuy.(float64) == 1 {
		query = query.Where("(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE user_id = brb.user_id)", ">=", 1)
	}
	if isRepeatBuy, ok := params["is_repeat_buy"]; ok && isRepeatBuy.(float64) == 0 {
		query = query.Where("(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE user_id = brb.user_id)", "=", 0)
	}
	// 复购次数
	if repeatBuyNum, ok := params["repeat_buy_num"]; ok && repeatBuyNum.(float64) > 0 {
		query = query.Where("(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE user_id = brb.user_id)", "=", repeatBuyNum)
	}

	return query
}

// buildOverdueBillListQuery 构建逾期账单列表查询对象
func (r *Repository) buildOverdueBillListQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("business_repayment_bills brb")

	// 添加关联查询
	query = query.
		LeftJoin("business_app_account baa", "brb.user_id = baa.id").
		LeftJoin("business_loan_orders blo", "brb.order_id = blo.id").
		LeftJoin("channel c", "blo.channel_id = c.id").
		LeftJoin("product_rules pr", "blo.product_rule_id = pr.id").
		LeftJoin("business_account sales_user", "blo.sales_assignee_id = sales_user.id").
		LeftJoin("business_account collection_user", "brb.collection_assignee_id = collection_user.id")

	// 应用筛选条件
	query = r.applyOverdueBillListFilters(query, params)

	return query
}

// applyOverdueBillListFilters 应用逾期账单列表筛选条件
func (r *Repository) applyOverdueBillListFilters(query gform.IOrm, params map[string]interface{}) gform.IOrm {
	//筛选。客户姓名、客户手机、订单编号、渠道来源、是否分配催收、催收姓名、逾期时间、最近催收时间
	// 订单编号查询
	if orderNo, ok := params["order_no"]; ok && orderNo != "" {
		query = query.Where("blo.order_no", "like", fmt.Sprintf("%%%v%%", orderNo))
	}

	// 用户ID查询
	if userID, ok := params["user_id"]; ok && userID != "" {
		query = query.Where("brb.user_id", userID)
	}

	// 用户姓名查询
	if userName, ok := params["user_name"]; ok && userName != "" {
		query = query.Where("baa.name", "like", fmt.Sprintf("%%%v%%", userName))
	}

	// 用户手机号查询
	if userMobile, ok := params["user_mobile"]; ok && userMobile != "" {
		query = query.Where("baa.mobile", "like", fmt.Sprintf("%%%v%%", userMobile))
	}

	// 业务员姓名查询
	if salesAssigneeName, ok := params["sales_assignee_name"]; ok && salesAssigneeName != "" {
		query = query.Where("sales_user.username", "like", fmt.Sprintf("%%%v%%", salesAssigneeName))
	}

	// 业务员姓名查询
	if salesAssigneeID, ok := params["sales_assignee_id"]; ok {
		query = query.Where("sales_user.id", salesAssigneeID)
	}

	// 催收员姓名查询
	if collectionAssigneeName, ok := params["collection_assignee_name"]; ok && collectionAssigneeName != "" {
		query = query.Where("collection_user.username", "like", fmt.Sprintf("%%%v%%", collectionAssigneeName))
	}
	// 催收员id查询
	if collectionAssigneeID, ok := params["collection_assignee_id"]; ok {
		query = query.Where("collection_user.id", collectionAssigneeID)
	}

	// 渠道名称查询
	if channelName, ok := params["channel_name"]; ok && channelName != "" {
		query = query.Where("c.channel_name", "like", fmt.Sprintf("%%%v%%", channelName))
	}

	// 渠道id查询
	if channelID, ok := params["channel_id"]; ok {
		query = query.Where("c.id", channelID)
	}

	// 最近催收时间范围查询
	if collectionTimeStart, ok := params["collection_time_start"]; ok && collectionTimeStart != "" {
		query = query.Where("brb.last_collection_time", ">=", collectionTimeStart)
	}

	if collectionTimeEnd, ok := params["collection_time_end"]; ok && collectionTimeEnd != "" {
		query = query.Where("brb.last_collection_time", "<=", collectionTimeEnd)
	}

	// 逾期日期范围查询
	if overdueDateStart, ok := params["overdue_date_start"]; ok && overdueDateStart != "" {
		overdue, _ := time.Parse("2006-01-02", overdueDateStart.(string))
		due := overdue.AddDate(0, 0, -1)
		query = query.Where("brb.due_date", ">=", due.Format("2006-01-02"))
	}

	if overdueDateEnd, ok := params["overdue_date_end"]; ok && overdueDateEnd != "" {
		overdue, _ := time.Parse("2006-01-02", overdueDateEnd.(string))
		due := overdue.AddDate(0, 0, -1)
		query = query.Where("brb.due_date", "<=", due.Format("2006-01-02"))
	}

	// 是否分配催收
	if isDistributeCollection, ok := params["is_distribute_collection"]; ok && isDistributeCollection.(float64) == 1 {
		query = query.Where("brb.collection_assignee_id is not null")
	}
	if isDistributeCollection, ok := params["is_distribute_collection"]; ok && isDistributeCollection.(float64) == 0 {
		query = query.Where("brb.collection_assignee_id is null")
	}
	query = query.Where("brb.status in (2, 3, 9)")

	return query
}

// applyOrderListFilters 应用订单列表筛选条件
func (r *Repository) applyOrderListFilters(query gform.IOrm, params map[string]interface{}) gform.IOrm {
	// 订单编号查询
	if orderNo, ok := params["order_no"]; ok && orderNo != "" {
		query = query.Where("blo.order_no", "like", fmt.Sprintf("%%%v%%", orderNo))
	}

	// 用户ID查询
	if userID, ok := params["user_id"]; ok && userID != "" {
		query = query.Where("blo.user_id", userID)
	}

	// 用户姓名查询
	if userName, ok := params["user_name"]; ok && userName != "" {
		query = query.Where("baa.name", "like", fmt.Sprintf("%%%v%%", userName))
	}

	// 用户身份证号查询
	if userIDCard, ok := params["user_id_card"]; ok && userIDCard != "" {
		query = query.Where("baa.idCard", "like", fmt.Sprintf("%%%v%%", userIDCard))
	}

	// 用户手机号查询
	if userMobile, ok := params["user_mobile"]; ok && userMobile != "" {
		query = query.Where("baa.mobile", "like", fmt.Sprintf("%%%v%%", userMobile))
	}

	// 业务员姓名查询
	if salesAssigneeName, ok := params["sales_assignee_name"]; ok && salesAssigneeName != "" {
		query = query.Where("sales_user.name", "like", fmt.Sprintf("%%%v%%", salesAssigneeName))
	}

	// 产品规则ID查询
	if productRuleID, ok := params["product_rule_id"]; ok && productRuleID != "" {
		query = query.Where("blo.product_rule_id", productRuleID)
	}

	// 借款金额范围查询
	if loanAmountMin, ok := params["loan_amount_min"]; ok && loanAmountMin != "" {
		query = query.Where("blo.loan_amount", ">=", loanAmountMin)
	}

	if loanAmountMax, ok := params["loan_amount_max"]; ok && loanAmountMax != "" {
		query = query.Where("blo.loan_amount", "<=", loanAmountMax)
	}

	// 订单状态查询
	if status, ok := params["status"]; ok && status != "" {
		query = query.Where("blo.status", status)
	}

	// 关单原因查询
	if reasonForClosure, ok := params["reason_for_closure"]; ok && reasonForClosure != "" {
		query = query.Where("blo.reason_for_closure", reasonForClosure)
	}

	// 冻结状态查询
	if isFreeze, ok := params["is_freeze"]; ok && isFreeze != "" {
		query = query.Where("blo.is_freeze", isFreeze)
	}

	// 投诉状态查询
	if complaintStatus, ok := params["complaint_status"]; ok && complaintStatus != "" {
		query = query.Where("blo.complaint_status", complaintStatus)
	}

	// 复审状态查询
	if reviewStatus, ok := params["review_status"]; ok && reviewStatus != "" {
		query = query.Where("blo.review_status", reviewStatus)
	}

	// 业务员ID查询
	if salesAssigneeID, ok := params["sales_assignee_id"]; ok && salesAssigneeID != "" {
		query = query.Where("blo.sales_assignee_id", salesAssigneeID)
	}

	// 是否已分配业务员查询
	if isSalesAssigned, ok := params["is_sales_assigned"]; ok && isSalesAssigned != "" {
		assignedVal := convert.MustConvertToInt(isSalesAssigned, -1)
		switch assignedVal {
		case 1:
			// 已分配：sales_assignee_id IS NOT NULL AND sales_assignee_id > 0
			query = query.Where("blo.sales_assignee_id IS NOT NULL AND blo.sales_assignee_id > 0")
		case 0:
			// 未分配：sales_assignee_id IS NULL OR sales_assignee_id = 0
			query = query.Where("(blo.sales_assignee_id IS NULL OR blo.sales_assignee_id = 0)")
		}
	}

	// 催收员ID查询
	if collectionAssigneeID, ok := params["collection_assignee_id"]; ok && collectionAssigneeID != "" {
		query = query.Where("blo.collection_assignee_id", collectionAssigneeID)
	}

	// 渠道ID查询
	if channelID, ok := params["channel_id"]; ok && channelID != "" {
		query = query.Where("blo.channel_id", channelID)
	}

	// 初始订单渠道ID查询
	if initialOrderChannelID, ok := params["initial_order_channel_id"]; ok && initialOrderChannelID != "" {
		query = query.Where("baa.channelId", initialOrderChannelID)
	}

	// 客户来源查询
	if customerOrigin, ok := params["customer_origin"]; ok && customerOrigin != "" {
		query = query.Where("blo.customer_origin", "like", fmt.Sprintf("%%%v%%", customerOrigin))
	}

	// 支付渠道ID查询
	if paymentChannelID, ok := params["payment_channel_id"]; ok && paymentChannelID != "" {
		query = query.Where("blo.payment_channel_id", paymentChannelID)
	}

	// 是否需要退款查询
	if isRefundNeeded, ok := params["is_refund_needed"]; ok && isRefundNeeded != "" {
		query = query.Where("blo.is_refund_needed", isRefundNeeded)
	}

	// 创建时间范围查询（对应SQL表中的created_at字段）
	if createdAtStart, ok := params["created_at_start"]; ok && createdAtStart != "" {
		t := carbon.Parse(createdAtStart.(string)).StartOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", ">=", t)
		}
	}

	if createdAtEnd, ok := params["created_at_end"]; ok && createdAtEnd != "" {
		t := carbon.Parse(createdAtEnd.(string)).EndOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", "<=", t)
		}
	}

	// 兼容submitted_at参数名（映射到created_at）
	if submittedAtStart, ok := params["submitted_at_start"]; ok && submittedAtStart != "" {
		t := carbon.Parse(submittedAtStart.(string)).StartOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", ">=", t)
		}
	}

	if submittedAtEnd, ok := params["submitted_at_end"]; ok && submittedAtEnd != "" {
		t := carbon.Parse(submittedAtEnd.(string)).EndOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", "<=", t)
		}
	}

	// 放款时间范围查询
	if disbursedAtStart, ok := params["disbursed_at_start"]; ok && disbursedAtStart != "" {
		query = query.Where("blo.disbursed_at", ">=", disbursedAtStart)
	}

	if disbursedAtEnd, ok := params["disbursed_at_end"]; ok && disbursedAtEnd != "" {
		query = query.Where("blo.disbursed_at", "<=", disbursedAtEnd)
	}

	// 完成时间范围查询
	if completedAtStart, ok := params["completed_at_start"]; ok && completedAtStart != "" {
		query = query.Where("blo.completed_at", ">=", completedAtStart)
	}

	if completedAtEnd, ok := params["completed_at_end"]; ok && completedAtEnd != "" {
		query = query.Where("blo.completed_at", "<=", completedAtEnd)
	}

	// 账单到期时间筛选 - 使用EXISTS子查询
	billDueDateStart, hasBillStart := params["bill_due_date_start"]
	billDueDateEnd, hasBillEnd := params["bill_due_date_end"]

	if (hasBillStart && billDueDateStart != "") || (hasBillEnd && billDueDateEnd != "") {
		existsQuery := "EXISTS (SELECT 1 FROM business_repayment_bills brb WHERE brb.order_id = blo.id"

		if hasBillStart && billDueDateStart != "" {
			existsQuery += " AND brb.due_date >= '" + fmt.Sprintf("%v", billDueDateStart) + "'"
		}

		if hasBillEnd && billDueDateEnd != "" {
			existsQuery += " AND brb.due_date <= '" + fmt.Sprintf("%v", billDueDateEnd) + "'"
		}

		existsQuery += ")"
		query = query.Where(existsQuery)
	}

	return query
}

// GetRiskScoresBatch 批量获取风控分数
func (r *Repository) GetRiskScoresBatch(userIDs []int) (map[int]int, error) {
	result := make(map[int]int)

	// 如果用户ID列表为空，直接返回空结果
	if len(userIDs) == 0 {
		return result, nil
	}

	// 构建IN查询条件
	userIDsStr := make([]string, len(userIDs))
	for i, id := range userIDs {
		userIDsStr[i] = fmt.Sprintf("%d", id)
	}

	// 查询每个用户最新的风控评估记录
	query := `
		SELECT re.customer_id, re.risk_score
		FROM risk_evaluations re
		INNER JOIN (
			SELECT customer_id, MAX(created_at) as latest_time
			FROM risk_evaluations
			WHERE customer_id IN (` + strings.Join(userIDsStr, ",") + `)
			GROUP BY customer_id
		) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time
	`

	riskScores, err := model.DB().Query(query)
	if err != nil {
		return nil, fmt.Errorf("批量查询风控分数失败: %v", err)
	}

	// 将查询结果转换为map
	for _, item := range riskScores {
		customerID := convert.GetIntFromMap(item, "customer_id", 0)
		riskScore := convert.GetIntFromMap(item, "risk_score", 0)
		if customerID > 0 {
			result[customerID] = riskScore
		}
	}

	return result, nil
}

// UpdateUserRemainingAmount 更新用户剩余额度,可以是占用或者撤回
func (r *Repository) UpdateUserRemainingAmount(tx gform.IOrm, order *model.BusinessLoanOrders, loanAmount float64, isWithdraw bool) error {
	userID := int64(order.UserID)

	// 查询用户剩余额
	userInfoMap, err := model.GetUserByID(userID)
	if err != nil {
		return fmt.Errorf("查询用户剩余额度失败: %v", err)
	}

	if len(userInfoMap) == 0 {
		return fmt.Errorf("用户不存在")
	}

	var handler gform.IOrm
	if tx != nil {
		handler = tx
	} else {
		handler = model.DB(model.WithContext(r.ctx))

	}

	// 查询渠道信息
	channelService := model.NewChannelService()
	channel, err := channelService.GetChannelByID(order.ChannelID)
	if err != nil {
		return fmt.Errorf("查询渠道信息失败: %v", err)
	}

	// 总额度
	allQuota := gconv.Float64(userInfoMap["allQuota"])
	// 剩余额度
	reminderQuota := gconv.Float64(userInfoMap["reminderQuota"])
	// 已提升的额度
	amountOfPromotion := gconv.Float64(userInfoMap["amountOfPromotion"])
	// 可提升额度
	everPromotionAmount := channel.RiskControl1Limit
	// 可提升额度上限
	everPromotionAmountLimit := channel.TotalAmount

	// 是否可提升额度
	var canLift bool
	if shopspringutils.CompareAmountsWithDecimal(allQuota, everPromotionAmountLimit) < 0 && isWithdraw {
		canLift = true
	}

	var remainingAmount float64
	if isWithdraw {
		remainingAmount = shopspringutils.AddAmountsWithDecimal(reminderQuota, loanAmount)
	} else {
		remainingAmount = shopspringutils.SubtractAmountsWithDecimal(reminderQuota, loanAmount)
	}

	updateMap := map[string]interface{}{
		"reminderQuota": remainingAmount,
	}

	// 提升额度
	if canLift {
		remainingAmount = shopspringutils.AddAmountsWithDecimal(remainingAmount, everPromotionAmount)
		amountOfPromotion = shopspringutils.AddAmountsWithDecimal(amountOfPromotion, everPromotionAmount)
		allQuota = shopspringutils.AddAmountsWithDecimal(allQuota, everPromotionAmount)
		updateMap["reminderQuota"] = remainingAmount
		updateMap["allQuota"] = allQuota
		updateMap["amountOfPromotion"] = amountOfPromotion
	}

	// 更新用户剩余额度
	num, err := handler.Table("business_app_account").
		Where("id", userID).
		Data(updateMap).
		Update()

	if err != nil {
		return fmt.Errorf("更新用户剩余额度失败: %v", err)
	}

	if num == 0 {
		return fmt.Errorf("更新用户剩余额度失败: 用户不存在")
	}

	return nil
}

// GetOrderListOptimized 获取订单列表
// 使用协程并行查询和查询结构优化来提升性能
func (r *Repository) GetOrderListOptimized(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	var (
		mainData        []gform.Data
		totalCount      int64
		paidPeriodsData map[int]int
		g               *errgroup.Group
	)

	g, _ = errgroup.WithContext(r.ctx)

	// 1. 并行执行主查询（订单基础信息 + 用户信息 + 主要渠道信息）
	g.Go(func() error {
		// 构建主查询
		mainQuery := r.buildOptimizedMainQuery(params)
		dataQuery := mainQuery.
			Fields(`blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,
			blo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,
			blo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,
			blo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,
			blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,								
			blo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at, blo.completed_at, blo.contract_id,
			sales_user.name as sales_assignee_name,
			baa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,
			c1.channel_name as channel_name, bpc.channel_name as payment_channel_name,
			c2.channel_name as initial_order_channel_name,
			pr.loan_period as loan_period, pr.total_periods as total_periods`).
			OrderBy("blo.id DESC")

		// 执行主数据查询
		result, err := dataQuery.Offset(paginationReq.GetOffset()).Limit(paginationReq.GetLimit()).Get()
		if err != nil {
			return fmt.Errorf("主查询失败: %v", err)
		}
		mainData = result
		return nil
	})

	// 2. 并行执行总数查询
	g.Go(func() error {
		countQuery := r.buildOptimizedMainQuery(params)
		total, err := countQuery.Count()
		if err != nil {
			return fmt.Errorf("总数查询失败: %v", err)
		}
		totalCount = total
		return nil
	})

	err := g.Wait()
	if err != nil {
		return nil, fmt.Errorf("查询失败: %v", err)
	}

	// 如果没有数据，直接返回
	if len(mainData) == 0 {
		return &pagination.PaginationResponse{
			Data:       mainData,
			Total:      totalCount,
			Page:       paginationReq.Page,
			PageSize:   paginationReq.PageSize,
			TotalPages: int(math.Ceil(float64(totalCount) / float64(paginationReq.PageSize))),
		}, nil
	}

	// 提取订单ID
	orderIDs := make([]int, 0, len(mainData))
	for _, row := range mainData {
		if orderID, ok := row["id"].(int64); ok {
			orderIDs = append(orderIDs, int(orderID))
		}
	}

	// 查询已付期数
	if len(orderIDs) > 0 {
		result, err := model.DB().Table("business_repayment_bills").
			Fields("order_id, COUNT(*) as paid_count").
			Where("order_id", "in", orderIDs).
			Where("status", 1).
			GroupBy("order_id").
			Get()
		if err != nil {
			return nil, fmt.Errorf("已付期数查询失败: %v", err)
		}

		data := make(map[int]int)
		for _, row := range result {
			if orderID, ok := row["order_id"].(int64); ok {
				if count, ok := row["paid_count"].(int64); ok {
					data[int(orderID)] = int(count)
				}
			}
		}

		paidPeriodsData = data
	}

	// 5. 组装最终结果
	finalData := r.assembleOrderListData(mainData, paidPeriodsData)

	// 计算总页数
	totalPages := int(math.Ceil(float64(totalCount) / float64(paginationReq.PageSize)))

	return &pagination.PaginationResponse{
		Data:       finalData,
		Total:      totalCount,
		Page:       paginationReq.Page,
		PageSize:   paginationReq.PageSize,
		TotalPages: totalPages,
	}, nil
}

// buildOptimizedMainQuery 构建优化的主查询
func (r *Repository) buildOptimizedMainQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("business_loan_orders blo")

	// 只保留必要的关联查询
	query = query.
		LeftJoin("business_app_account baa", "blo.user_id = baa.id").
		LeftJoin("channel c1", "blo.channel_id = c1.id").
		LeftJoin("channel c2", "baa.channelId = c2.id").
		LeftJoin("product_rules pr", "blo.product_rule_id = pr.id").
		LeftJoin("business_payment_channels bpc", "blo.payment_channel_id = bpc.id").
		LeftJoin("business_account sales_user", "blo.sales_assignee_id = sales_user.id")

	// 应用筛选条件
	query = r.applyOrderListFilters(query, params)

	return query
}

// assembleOrderListData 组装订单列表数据
func (r *Repository) assembleOrderListData(
	mainData []gform.Data,
	paidPeriodsData map[int]int,
) []gform.Data {
	for i, row := range mainData {
		// 添加已付期数
		if orderID, ok := row["id"].(int64); ok {
			if paidCount, exists := paidPeriodsData[int(orderID)]; exists {
				mainData[i]["paid_periods"] = paidCount
			} else {
				mainData[i]["paid_periods"] = 0
			}
		}
	}

	return mainData
}
