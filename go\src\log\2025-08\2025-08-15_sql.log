{"level":"dev.info","ts":"[2025-08-15 10:24:38.193]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf8b928ca7ec98fda3db","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3]","duration":"36.202ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-15 10:24:38.194]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf8b928ca7ec98fda3db","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and blo.disbursed_at = ? LIMIT 1, [1 3 IS NOT NULL]","duration":"37.2124ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-15 10:24:38.213]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf8b928ca7ec98fda3db","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3]","duration":"19.1219ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-15 10:24:38.213]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf8b928ca7ec98fda3db","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) GROUP BY blo.user_id, [1 3]","duration":"19.1219ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-15 10:25:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"49.4161ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-15 10:25:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"49.4161ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-15 10:25:18.004]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf94d7f7588ce8225850","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) LIMIT 1, [1 3]","duration":"24.131ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-15 10:25:18.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf94d7f7588ce8225850","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3]","duration":"64.5915ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-08-15 10:25:18.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf94d7f7588ce8225850","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) GROUP BY blo.user_id, [1 3]","duration":"38.9548ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-15 10:25:18.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcf94d7f7588ce8225850","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3]","duration":"21.309ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-15 10:26:00.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"25.6898ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-15 10:26:00.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"45.0772ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-15 10:26:00.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"58.7511ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-15 10:26:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"60.7812ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-15 10:26:08.008]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa07cff46a45ea18bdc","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3 2025-08-15 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"17.991ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-15 10:26:08.009]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa07cff46a45ea18bdc","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? LIMIT 1, [1 3 2025-08-15 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"18.4987ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-15 10:26:08.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa07cff46a45ea18bdc","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3 2025-08-15 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"18.0811ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-15 10:26:08.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa07cff46a45ea18bdc","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? GROUP BY blo.user_id, [1 3 2025-08-15 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"21.4571ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-15 10:26:21.228]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa38fe0d6e053561605","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? LIMIT 1, [1 3 2025-08-13 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"35.8745ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-15 10:26:21.233]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa38fe0d6e053561605","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3 2025-08-13 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"40.7037ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-15 10:26:21.259]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa38fe0d6e053561605","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3 2025-08-13 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"26.3648ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-15 10:26:21.263]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa38fe0d6e053561605","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? GROUP BY blo.user_id, [1 3 2025-08-13 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"34.4762ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-15 10:26:34.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa699d2c00cfe5d3788","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? LIMIT 1, [1 3 2025-08-12 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"19.4236ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-15 10:26:34.267]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa699d2c00cfe5d3788","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3 2025-08-12 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"23.1544ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-15 10:26:34.288]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa699d2c00cfe5d3788","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3 2025-08-12 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"20.2703ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-15 10:26:34.288]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfa699d2c00cfe5d3788","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and blo.disbursed_at BETWEEN ? and ? GROUP BY blo.user_id, [1 3 2025-08-12 00:00:00 +0800 CST 2025-08-15 23:59:59.********* +0800 CST]","duration":"22.4289ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-15 10:26:59.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfac60519050ab166b04","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3]","duration":"69.6409ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-15 10:26:59.125]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfac60519050ab166b04","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) LIMIT 1, [1 3]","duration":"77.2561ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-08-15 10:26:59.153]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfac60519050ab166b04","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3]","duration":"34.7558ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-15 10:26:59.157]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfac60519050ab166b04","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) GROUP BY blo.user_id, [1 3]","duration":"31.8629ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-15 10:27:00.015]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"15.0919ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-15 10:27:00.015]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"15.5988ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-15 10:27:05.289]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfadd26865f04fb0b90c","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3]","duration":"31.8859ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-15 10:27:05.291]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfadd26865f04fb0b90c","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) LIMIT 1, [1 3]","duration":"34.3069ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-15 10:27:05.320]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfadd26865f04fb0b90c","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 10 OFFSET 10, [1 3]","duration":"30.3472ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-15 10:27:05.320]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfadd26865f04fb0b90c","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) GROUP BY blo.user_id, [1 3]","duration":"29.002ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-15 10:28:54.985]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc75e015e646e34cc18","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) LIMIT 1, [1 3]","duration":"10.6133ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-15 10:28:54.993]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc75e015e646e34cc18","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) GROUP BY blo.user_id, [1 3]","duration":"7.4979ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-15 10:28:55.001]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc75e015e646e34cc18","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3]","duration":"27.3303ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-15 10:28:55.034]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc75e015e646e34cc18","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 10 OFFSET 10, [1 3]","duration":"32.1837ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-15 10:29:00.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"15.1911ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-15 10:29:00.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"15.1911ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-15 10:29:00.585]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc8ab888a3024c7ea49","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3]","duration":"15.3229ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-15 10:29:00.585]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc8ab888a3024c7ea49","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) LIMIT 1, [1 3]","duration":"15.3229ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-15 10:29:00.605]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc8ab888a3024c7ea49","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3]","duration":"19.315ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-15 10:29:00.605]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfc8ab888a3024c7ea49","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) GROUP BY blo.user_id, [1 3]","duration":"19.315ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-15 10:30:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"52.317ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-15 10:30:00.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"58.5953ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-15 10:30:00.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"58.5953ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-15 10:30:00.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"58.5953ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-15 10:30:45.335]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe10e5d99a8426e3486","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3 %1919%]","duration":"28.1297ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-15 10:30:45.335]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe10e5d99a8426e3486","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? LIMIT 1, [1 3 %1919%]","duration":"28.1297ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-15 10:30:45.368]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe10e5d99a8426e3486","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3 %1919%]","duration":"33.3463ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-15 10:30:45.369]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe10e5d99a8426e3486","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? GROUP BY blo.user_id, [1 3 %1919%]","duration":"33.8504ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-15 10:30:54.209]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe31ef13110adaf954e","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3 %191222%]","duration":"34.181ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-15 10:30:54.210]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe31ef13110adaf954e","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? LIMIT 1, [1 3 %191222%]","duration":"35.4889ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-15 10:30:54.223]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe31ef13110adaf954e","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3 %191222%]","duration":"13.9437ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-15 10:30:54.224]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe31ef13110adaf954e","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.mobile LIKE ? GROUP BY blo.user_id, [1 3 %191222%]","duration":"13.6429ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-15 10:31:00.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"36.9712ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-15 10:31:00.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"36.9712ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-15 10:31:04.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe57e64f5bcea3fffbc","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? LIMIT 1, [1 3 %尹%]","duration":"12.1616ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-15 10:31:04.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe57e64f5bcea3fffbc","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3 %尹%]","duration":"12.1616ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-15 10:31:04.390]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe57e64f5bcea3fffbc","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3 %尹%]","duration":"12.4684ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-15 10:31:04.390]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe57e64f5bcea3fffbc","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? GROUP BY blo.user_id, [1 3 %尹%]","duration":"12.4684ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-15 10:31:07.119]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe62062b98036723e11","sql":"SELECT COALESCE(SUM(blo.principal), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT blo.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(blo.disbursed_at) = CURDATE() THEN blo.principal ELSE 0 END), 0) as today_expense FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? LIMIT 1, [1 3 %尹2%]","duration":"35.3896ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-15 10:31:07.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe62062b98036723e11","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 1, [1 3 %尹2%]","duration":"36.8956ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-15 10:31:07.146]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe62062b98036723e11","sql":"SELECT blo.order_no,blo.principal,baa.name as user_name,baa.mobile,bbc.bank_card_no,blo.disbursed_at,blo.user_id FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? ORDER BY blo.disbursed_at DESC LIMIT 10, [1 3 %尹2%]","duration":"25.9805ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-15 10:31:07.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcfe62062b98036723e11","sql":"SELECT blo.user_id,SUM(blo.principal) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = blo.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id WHERE blo.status IN (?,?) and (blo.disbursed_at IS NOT NULL) and baa.name LIKE ? GROUP BY blo.user_id, [1 3 %尹2%]","duration":"26.5082ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-15 14:21:37.668]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bdc7a3cfd53c03c10e278","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"301.1299ms","duration_ms":301}
{"level":"dev.info","ts":"[2025-08-15 14:21:37.960]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bdc7a3cfd53c03c10e278","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at,cs.updated_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"290.1468ms","duration_ms":290}
{"level":"dev.info","ts":"[2025-08-15 14:22:00.022]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"14.8416ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-15 14:22:00.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"34.9026ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-15 14:22:00.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"43.3707ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-15 14:22:00.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"44.3786ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-15 14:22:31.235]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bdc86c50cd39ce39cdd94","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) LIMIT 1, []","duration":"51.718ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-15 14:22:31.244]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bdc86c50cd39ce39cdd94","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at,cs.updated_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) ORDER BY cs.created_at DESC LIMIT 10, []","duration":"9.4298ms","duration_ms":9}
