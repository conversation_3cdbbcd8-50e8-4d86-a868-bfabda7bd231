{"level":"dev.info","ts":"[2025-08-15 09:10:47.431]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcb83f1925b1c9bb29ab9","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","query":"channel_id&due_date_start&due_date_end&period_number&is_new_user&page_size=10","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0771228,"request_size":0,"response_size":1189}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.291]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcb8512b06d74c2e3b202","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","query":"channel_id&due_date_start&due_date_end&period_number&is_new_user&page_size=10","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0950518,"request_size":0,"response_size":1189}
{"level":"dev.info","ts":"[2025-08-15 10:19:28.552]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcf4379137ff453c66bb2","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start&date_end&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0583009,"request_size":0,"response_size":346}
{"level":"dev.info","ts":"[2025-08-15 10:20:38.357]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcf53b91b772cc7047621","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start&date_end&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0684796,"request_size":0,"response_size":346}
{"level":"dev.info","ts":"[2025-08-15 10:22:11.056]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcf694e89335c2a59ef49","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start&date_end&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0686902,"request_size":0,"response_size":2528}
{"level":"dev.info","ts":"[2025-08-15 10:24:21.158]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcf866eec4c84375fdd9e","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start&date_end&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":5.0730394,"request_size":0,"response_size":2528}
{"level":"dev.info","ts":"[2025-08-15 10:24:38.214]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcf8b928ca7ec98fda3db","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start&date_end&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0573634,"request_size":0,"response_size":2528}
{"level":"dev.info","ts":"[2025-08-15 10:25:18.070]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcf94d7f7588ce8225850","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start&date_end&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0912639,"request_size":0,"response_size":2533}
{"level":"dev.info","ts":"[2025-08-15 10:26:08.032]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfa07cff46a45ea18bdc","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start=2025-08-15&date_end=2025-08-15&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0425574,"request_size":0,"response_size":570}
{"level":"dev.info","ts":"[2025-08-15 10:26:21.264]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfa38fe0d6e053561605","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start=2025-08-13&date_end=2025-08-15&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0728991,"request_size":0,"response_size":570}
{"level":"dev.info","ts":"[2025-08-15 10:26:34.288]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfa699d2c00cfe5d3788","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"date_start=2025-08-12&date_end=2025-08-15&channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.044978,"request_size":0,"response_size":794}
{"level":"dev.info","ts":"[2025-08-15 10:26:59.157]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfac60519050ab166b04","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name&mobile&page&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.1096358,"request_size":0,"response_size":2533}
{"level":"dev.info","ts":"[2025-08-15 10:27:05.320]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfadd26865f04fb0b90c","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name&mobile&page=2&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0633089,"request_size":0,"response_size":976}
{"level":"dev.info","ts":"[2025-08-15 10:28:55.035]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfc75e015e646e34cc18","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name&mobile&page=2&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0612212,"request_size":0,"response_size":976}
{"level":"dev.info","ts":"[2025-08-15 10:29:00.606]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfc8ab888a3024c7ea49","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name&mobile&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0371808,"request_size":0,"response_size":2533}
{"level":"dev.info","ts":"[2025-08-15 10:30:45.369]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfe10e5d99a8426e3486","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name&mobile=1919&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0631955,"request_size":0,"response_size":2533}
{"level":"dev.info","ts":"[2025-08-15 10:30:54.224]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfe31ef13110adaf954e","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name&mobile=191222&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0501937,"request_size":0,"response_size":346}
{"level":"dev.info","ts":"[2025-08-15 10:31:04.391]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfe57e64f5bcea3fffbc","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name=%E5%B0%B9&mobile=&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.025133,"request_size":0,"response_size":2533}
{"level":"dev.info","ts":"[2025-08-15 10:31:07.147]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcfe62062b98036723e11","method":"GET","url":"/business/statistics/statisticscontroller/getExpenseDetails","query":"channel_id&user_name=%E5%B0%B92&mobile=&page_size","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0634038,"request_size":0,"response_size":346}
{"level":"dev.info","ts":"[2025-08-15 14:21:37.962]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bdc7a3cfd53c03c10e278","method":"GET","url":"/business/statistics/statisticscontroller/getChannelStatistics","query":"page&page_size&date","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.5962079,"request_size":0,"response_size":2396}
{"level":"dev.info","ts":"[2025-08-15 14:22:31.245]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bdc86c50cd39ce39cdd94","method":"GET","url":"/business/statistics/statisticscontroller/getChannelStatistics","query":"page&page_size&date","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0633638,"request_size":0,"response_size":2336}
